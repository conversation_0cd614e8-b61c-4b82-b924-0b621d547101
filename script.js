// B4J REST API Server Configuration
const API_BASE_URL = 'https://indexbuddy.co.za/manne';

// Current section tracking
let currentSection = 'dashboard';

let currentContacts = [];
let mcpInitialized = false; // Track if MCP server is initialized
let currentPage = 1;

// Create Smithery URL with configuration (similar to createSmitheryUrl from SDK)
function createSmitheryUrl(baseUrl, options) {
    const url = new URL(baseUrl);

    // Add API key
    url.searchParams.set('api_key', options.apiKey);

    // Add configuration as JSON
    if (options.config) {
        url.searchParams.set('config', JSON.stringify(options.config));
    }

    return url.toString();
}

// Parse Server-Sent Events response
function parseSSEResponse(sseText) {
    const lines = sseText.split('\n');
    const events = [];
    let currentEvent = {};

    for (const line of lines) {
        if (line.startsWith('event:')) {
            currentEvent.event = line.substring(6).trim();
        } else if (line.startsWith('data:')) {
            const dataStr = line.substring(5).trim();
            try {
                currentEvent.data = JSON.parse(dataStr);
            } catch (e) {
                currentEvent.data = dataStr;
            }
        } else if (line.trim() === '') {
            // Empty line indicates end of event
            if (currentEvent.event || currentEvent.data) {
                events.push(currentEvent);
                currentEvent = {};
            }
        }
    }

    // Add the last event if it exists
    if (currentEvent.event || currentEvent.data) {
        events.push(currentEvent);
    }

    return events;
}
const rowsPerPage = 10; // Number of rows per page
let authToken = null; // Store authentication token

// DOM Elements - Login
const loginForm = document.getElementById('loginForm');
const loginMessage = document.getElementById('loginMessage');
const loginScreen = document.getElementById('loginScreen');
const mainApplication = document.getElementById('mainApplication');
const logoutButton = document.getElementById('logoutButton');

// DOM Elements - Main App
const contactsTableBody = document.getElementById('contactsTableBody');
const searchInput = document.getElementById('searchInput');
const searchButton = document.getElementById('searchButton');
const groupFilter = document.getElementById('groupFilter');
const whatsappFilter = document.getElementById('whatsappFilter');
const birthdayFilter = document.getElementById('birthdayFilter');
const addButton = document.getElementById('addButton');
const editButton = document.getElementById('editButton');
const deleteButton = document.getElementById('deleteButton');
const selectAllCheckbox = document.getElementById('selectAll');
const itemsPerPageSelect = document.getElementById('itemsPerPage');
const loadingSpinner = document.getElementById('loadingSpinner');

// Login function - simplified for MCP server
async function login(event) {
    event.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    if (!username || !password) {
        showLoginError('Please enter both username and password');
        return;
    }

    try {
        loadingSpinner.style.display = 'block';

        // For MCP server, we'll use a simple authentication approach
        // Store credentials for API calls
        authToken = btoa(`${username}:${password}`); // Base64 encode credentials
        localStorage.setItem('authToken', authToken);
        localStorage.setItem('username', username);

        // Test connection to MCP server
        console.log('Testing MCP connection during login...');
        const testResponse = await testMCPConnection();
        console.log('Test response:', testResponse);

        if (testResponse && testResponse.success) {
            // Show main application
            showMainApplication();

            // Update message in the top bar
            const messageArea = document.getElementById('messageArea');
            if (messageArea) {
                messageArea.textContent = `Welcome, ${username}!`;
            }

            // Fetch contacts from live database
            await fetchContacts();
        } else {
            throw new Error('Authentication failed');
        }
    } catch (error) {
        console.error('Login error:', error);
        showLoginError('Login failed. Please check your credentials and try again.');
    } finally {
        loadingSpinner.style.display = 'none';
    }
}

// Initialize MCP server
async function initializeMCPServer() {
    console.log('initializeMCPServer called, mcpInitialized:', mcpInitialized);

    if (mcpInitialized) {
        console.log('MCP server already initialized, returning success');
        return { success: true };
    }

    try {
        console.log('Starting MCP server initialization...');

        const requestUrl = `${MCP_SERVER_URL}?api_key=${API_KEY}&profile=${PROFILE}`;

        const response = await fetch(requestUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/event-stream'
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                id: 1,
                method: 'initialize',
                params: {
                    protocolVersion: '2024-11-05',
                    capabilities: {
                        tools: {}
                    },
                    clientInfo: {
                        name: 'Contact Management System',
                        version: '1.0.0'
                    }
                }
            })
        });

        if (response.ok) {
            // The response might be Server-Sent Events format, so handle it as text first
            const responseText = await response.text();
            console.log('MCP server initialization response (raw):', responseText);

            try {
                // Try to parse as JSON first
                const data = JSON.parse(responseText);
                console.log('MCP server initialized successfully:', data);
                mcpInitialized = true;
                return { success: true, data };
            } catch (parseError) {
                // If it's not JSON, it might be SSE format - parse the event stream
                console.log('Response is not JSON, parsing as SSE...');
                const sseData = parseSSEResponse(responseText);
                console.log('MCP server initialized successfully (SSE):', sseData);
                mcpInitialized = true;
                return { success: true, data: sseData };
            }
        } else {
            const errorText = await response.text();
            console.error('MCP server initialization failed:', errorText);
            return { success: false, error: errorText };
        }
    } catch (error) {
        console.error('MCP server initialization error:', error);
        return { success: false, error: error.message };
    }
}

// Test MCP server connection - initialize first, then test
async function testMCPConnection() {
    console.log('Testing MCP server connection...');

    // First, initialize the MCP server
    const initResult = await initializeMCPServer();
    if (!initResult.success) {
        console.error('Failed to initialize MCP server:', initResult.error);
        return initResult;
    }

    // Now try calling a tool
    try {
        const testUrl = `${MCP_SERVER_URL}?api_key=${API_KEY}&profile=${PROFILE}`;

        const response = await fetch(testUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/event-stream'
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                id: 2,
                method: 'tools/call',
                params: {
                    name: 'run_sql_query',
                    arguments: {
                        query: 'SHOW TABLES'
                    }
                }
            })
        });

        if (response.ok) {
            const data = await response.json();
            console.log('MCP server connection successful:', data);
            return { success: true, data };
        } else {
            // Try to get the error details from the response
            let errorDetails = '';
            try {
                const errorData = await response.json();
                errorDetails = JSON.stringify(errorData, null, 2);
                console.error('MCP Server Connection Error Response:', errorData);
            } catch (e) {
                const errorText = await response.text();
                errorDetails = errorText;
                console.error('MCP Server Connection Error Text:', errorText);
            }
            console.error('MCP server connection failed:', response.status, errorDetails);
            return { success: false, error: `HTTP ${response.status}: ${errorDetails}` };
        }
    } catch (error) {
        console.error('MCP server connection error:', error);
        return { success: false, error: error.message };
    }
}

// Show login error message
function showLoginError(message) {
    loginMessage.textContent = message;
    loginMessage.classList.remove('d-none');

    // Hide the message after 3 seconds
    setTimeout(() => {
        loginMessage.classList.add('d-none');
    }, 3000);
}

// Show main application
function showMainApplication() {
    loginScreen.classList.add('d-none');
    mainApplication.classList.remove('d-none');
}

// Show login screen
function showLoginScreen() {
    mainApplication.classList.add('d-none');
    loginScreen.classList.remove('d-none');
    document.getElementById('username').value = '';
    document.getElementById('password').value = '';
    loginMessage.classList.add('d-none');
}

// Logout function
function logout() {
    // Clear authentication data
    authToken = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('fingerprint');

    // Show login screen
    showLoginScreen();
}

// Check if user is already logged in - BYPASSED FOR TESTING
function checkAuth() {
    // Bypass login for now - go straight to main application
    console.log('Login bypassed for testing - going straight to main application');

    // Set a temporary auth token
    authToken = 'bypass-token';

    // Show main application directly
    showMainApplication();

    // Update message in the top bar
    const messageArea = document.getElementById('messageArea');
    if (messageArea) {
        messageArea.textContent = 'Welcome! (Login bypassed for testing)';
    }

    // Fetch contacts from live database
    fetchContacts();
}

// Logout function
function logout() {
    // Clear authentication data
    authToken = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('username');

    // Show login screen
    showLoginScreen();
}

// Fetch brothers data from B4J REST API server
async function fetchBrothers() {
    try {
        loadingSpinner.style.display = 'block';

        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = 'Loading brothers from live database...';
        }

        console.log('=== FETCHING BROTHERS FROM B4J API ===');

        const apiUrl = `${API_BASE_URL}/555`;  // Same endpoint for now
        console.log('Brothers API URL:', apiUrl);

        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Raw brothers API response:', data);

        // Process the response data
        if (Array.isArray(data)) {
            currentContacts = data.map((row, index) => ({
                Serial: row.Serial || row.id || index + 1,
                ContactName: row.ContactName || row.name || `Brother - ${row.FirstName || row.firstName || ''}`,
                FirstName: row.FirstName || row.firstName || '',
                Contactnumber: row.Contactnumber || row.phone || row.contactNumber || '',
                ContactGroup: row.ContactGroup || row.group || '',
                WhatsApp: row.WhatsApp || row.whatsapp || 'No',
                Birthday: row.Birthday || row.birthday || ''
            }));
        } else if (data.brothers && Array.isArray(data.brothers)) {
            currentContacts = data.brothers.map((row, index) => ({
                Serial: row.Serial || row.id || index + 1,
                ContactName: row.ContactName || row.name || `Brother - ${row.FirstName || row.firstName || ''}`,
                FirstName: row.FirstName || row.firstName || '',
                Contactnumber: row.Contactnumber || row.phone || row.contactNumber || '',
                ContactGroup: row.ContactGroup || row.group || '',
                WhatsApp: row.WhatsApp || row.whatsapp || 'No',
                Birthday: row.Birthday || row.birthday || ''
            }));
        } else if (data.data && Array.isArray(data.data)) {
            currentContacts = data.data.map((row, index) => ({
                Serial: row.Serial || row.id || index + 1,
                ContactName: row.ContactName || row.name || `Brother - ${row.FirstName || row.firstName || ''}`,
                FirstName: row.FirstName || row.firstName || '',
                Contactnumber: row.Contactnumber || row.phone || row.contactNumber || '',
                ContactGroup: row.ContactGroup || row.group || '',
                WhatsApp: row.WhatsApp || row.whatsapp || 'No',
                Birthday: row.Birthday || row.birthday || ''
            }));
        } else {
            console.warn('Unexpected brothers data format:', data);
            currentContacts = [];
        }

        console.log('Processed brothers:', currentContacts);

        currentDataSource = 'brothers';

        // Update UI
        if (messageArea) {
            messageArea.textContent = `Loaded ${currentContacts.length} brothers from live database`;
        }

        document.querySelector('h1').textContent = 'Brothers List';
        populateContactGroups();
        filterAndDisplayContacts();

        console.log('=== BROTHERS B4J API CONNECTION SUCCESSFUL ===');

    } catch (error) {
        console.error('=== BROTHERS B4J API CONNECTION FAILED ===', error);

        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = 'Failed to load brothers from live database';
        }

        // Log error to console only (no alert popup)
        console.error('Brothers API connection error details logged above');

        throw error;
    } finally {
        loadingSpinner.style.display = 'none';
    }
}

// Dashboard Functions
async function loadDashboard() {
    try {
        console.log('Loading dashboard...');

        // Show dashboard section, hide others
        showSection('dashboard');

        // Update message area
        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = 'Loading dashboard data...';
        }

        // Fetch data for dashboard counts
        const apiUrl = `${API_BASE_URL}/555`;
        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const responseText = await response.text();
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            throw new Error('Invalid JSON response from server');
        }

        // Calculate dashboard statistics
        const totalContacts = data.length;
        const whatsappYes = data.filter(contact => contact.WhatsApp === 'Yes').length;
        const uniqueGroups = [...new Set(data.map(contact => contact.ContactGroup).filter(group => group))].length;

        // Calculate birthdays this month
        const currentMonth = new Date().getMonth() + 1;
        const birthdaysThisMonth = data.filter(contact => {
            if (contact.Birthday) {
                const birthMonth = new Date(contact.Birthday).getMonth() + 1;
                return birthMonth === currentMonth;
            }
            return false;
        }).length;

        // Update dashboard cards
        document.getElementById('totalContactsCount').textContent = totalContacts;
        document.getElementById('whatsappYesCount').textContent = whatsappYes;
        document.getElementById('totalGroupsCount').textContent = uniqueGroups;
        document.getElementById('birthdaysThisMonthCount').textContent = birthdaysThisMonth;

        // Update system status
        document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
        document.getElementById('totalRecords').textContent = totalContacts;

        if (messageArea) {
            messageArea.textContent = 'Dashboard loaded successfully';
        }

        console.log('Dashboard loaded successfully');

    } catch (error) {
        console.error('Error loading dashboard:', error);

        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = 'Error loading dashboard data';
        }

        // Set default values on error
        document.getElementById('totalContactsCount').textContent = '0';
        document.getElementById('whatsappYesCount').textContent = '0';
        document.getElementById('totalGroupsCount').textContent = '0';
        document.getElementById('birthdaysThisMonthCount').textContent = '0';
        document.getElementById('lastUpdate').textContent = 'Error';
        document.getElementById('totalRecords').textContent = '0';
    }
}

// Show specific section and hide others
function showSection(sectionName) {
    // Hide all sections
    document.getElementById('dashboardSection').classList.add('d-none');
    document.getElementById('brothersListSection').classList.add('d-none');
    document.getElementById('quickMessageSection').classList.add('d-none');

    // Update page title in top nav bar
    const pageTitle = document.getElementById('pageTitle');

    // Show requested section
    if (sectionName === 'dashboard') {
        document.getElementById('dashboardSection').classList.remove('d-none');
        currentSection = 'dashboard';
        if (pageTitle) pageTitle.textContent = 'Dashboard';
    } else if (sectionName === 'brothersList') {
        document.getElementById('brothersListSection').classList.remove('d-none');
        currentSection = 'brothersList';
        if (pageTitle) pageTitle.textContent = 'Brothers List';
    } else if (sectionName === 'quickMessage') {
        document.getElementById('quickMessageSection').classList.remove('d-none');
        currentSection = 'quickMessage';
        if (pageTitle) pageTitle.textContent = 'Quick Message';
    }
}

// Navigation functions for dashboard quick actions
function showBrothersList() {
    showSection('brothersList');
    fetchContacts(); // Load the contacts data
}

function addNewContact() {
    showSection('brothersList');
    // Open the add modal
    setTimeout(() => {
        const addButton = document.getElementById('addButton');
        if (addButton) {
            addButton.click();
        }
    }, 100);
}

async function showQuickMessage() {
    showSection('quickMessage');

    // Ensure contacts are loaded before initializing Quick Message
    if (currentContacts.length === 0) {
        console.log('Loading contacts for Quick Message...');
        await fetchContacts();
    }

    initializeQuickMessage();
}

function showCommunication() {
    showQuickMessage(); // Default to Quick Message for now
}

function showReports() {
    alert('Reports features coming soon!');
}

// Fetch contacts from B4J REST API server
async function fetchContacts() {
    try {
        loadingSpinner.style.display = 'block';

        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = 'Loading contacts from live database...';
        }

        console.log('=== CONNECTING TO B4J REST API ===');

        // Test your B4J server endpoint (matching your B4J code)
        const apiUrl = `${API_BASE_URL}/555`;
        console.log('Testing B4J API URL:', apiUrl);

        let response;
        try {
            // Try direct call first
            response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            });
        } catch (corsError) {
            console.log('Direct call failed due to CORS, trying alternative...');

            // Alternative: Try with different headers
            response = await fetch(apiUrl, {
                method: 'GET',
                mode: 'no-cors'
            });

            if (response.type === 'opaque') {
                throw new Error('CORS_ERROR: Your B4J server needs CORS headers. See console for solution.');
            }
        }

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // First, let's see the raw response text
        const responseText = await response.text();
        console.log('Raw response text from B4J server:', responseText);
        console.log('Response length:', responseText.length);

        // Try to parse as JSON
        let data;
        try {
            data = JSON.parse(responseText);
            console.log('Successfully parsed JSON:', data);
        } catch (parseError) {
            console.error('JSON Parse Error:', parseError);
            console.log('First 500 characters of response:', responseText.substring(0, 500));
            console.log('Last 500 characters of response:', responseText.substring(Math.max(0, responseText.length - 500)));
            throw new Error('Invalid JSON response from server: ' + parseError.message);
        }

        // Process the response data
        if (Array.isArray(data)) {
            // If data is directly an array of contacts
            currentContacts = data.map((row, index) => ({
                Serial: row.Serial || row.id || index + 1,
                ContactName: row.ContactName || row.name || '',
                FirstName: row.FirstName || row.firstName || '',
                Contactnumber: row.Contactnumber || row.phone || row.contactNumber || '',
                ContactGroup: row.ContactGroup || row.group || '',
                WhatsApp: row.WhatsApp || row.whatsapp || 'No',
                Birthday: row.Birthday || row.birthday || ''
            }));
        } else if (data.contacts && Array.isArray(data.contacts)) {
            // If data has a contacts property
            currentContacts = data.contacts.map((row, index) => ({
                Serial: row.Serial || row.id || index + 1,
                ContactName: row.ContactName || row.name || '',
                FirstName: row.FirstName || row.firstName || '',
                Contactnumber: row.Contactnumber || row.phone || row.contactNumber || '',
                ContactGroup: row.ContactGroup || row.group || '',
                WhatsApp: row.WhatsApp || row.whatsapp || 'No',
                Birthday: row.Birthday || row.birthday || ''
            }));
        } else if (data.data && Array.isArray(data.data)) {
            // If data has a data property
            currentContacts = data.data.map((row, index) => ({
                Serial: row.Serial || row.id || index + 1,
                ContactName: row.ContactName || row.name || '',
                FirstName: row.FirstName || row.firstName || '',
                Contactnumber: row.Contactnumber || row.phone || row.contactNumber || '',
                ContactGroup: row.ContactGroup || row.group || '',
                WhatsApp: row.WhatsApp || row.whatsapp || 'No',
                Birthday: row.Birthday || row.birthday || ''
            }));
        } else {
            console.warn('Unexpected data format:', data);
            currentContacts = [];
        }

        console.log('Processed contacts:', currentContacts);

        currentDataSource = 'contacts';

        // Update UI
        if (messageArea) {
            messageArea.textContent = `Loaded ${currentContacts.length} contacts from live database`;
        }

        document.querySelector('h1').textContent = 'Contact Management System';
        populateContactGroups();
        filterAndDisplayContacts();

        console.log('=== B4J API CONNECTION SUCCESSFUL ===');

    } catch (error) {
        console.error('=== B4J API CONNECTION FAILED ===', error);

        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = 'CORS Error - B4J Server needs configuration';
        }

        // Show detailed CORS solution
        console.log('');
        console.log('🔧 CORS SOLUTION FOR YOUR B4J SERVER:');
        console.log('Add these lines to your B4J server handler:');
        console.log('');
        console.log('resp.SetHeader("Access-Control-Allow-Origin", "*")');
        console.log('resp.SetHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")');
        console.log('resp.SetHeader("Access-Control-Allow-Headers", "Content-Type, Accept")');
        console.log('');
        console.log('If req.Method = "OPTIONS" Then');
        console.log('    resp.SendResponse(200, "OK", "")');
        console.log('    Return');
        console.log('End If');
        console.log('');

        // Log error to console only (no alert popup)
        console.error('Error details logged above for debugging');

        throw error;
    } finally {
        loadingSpinner.style.display = 'none';
    }
}

// Populate contact groups for filter
function populateContactGroups() {
    // Get unique contact groups
    const groups = [...new Set(currentContacts.map(contact => contact.ContactGroup).filter(Boolean))];

    // Clear existing options except the "All" option
    while (groupFilter.options.length > 1) {
        groupFilter.remove(1);
    }

    // Add options for each group
    groups.forEach(group => {
        const option = document.createElement('option');
        option.value = group;
        option.textContent = group;
        groupFilter.appendChild(option);
    });
}

// Filter and display contacts
function filterAndDisplayContacts() {
    let filteredContacts = [...currentContacts];

    // Apply filters
    const groupValue = groupFilter.value.toLowerCase();
    const whatsappValue = whatsappFilter.value;
    const birthdayValue = birthdayFilter.value;
    const searchValue = searchInput.value.toLowerCase();

    filteredContacts = filteredContacts.filter(contact => {
        const matchesGroup = !groupValue || contact.ContactGroup?.toString().toLowerCase().includes(groupValue);
        const matchesWhatsapp = !whatsappValue || contact.WhatsApp === whatsappValue;
        const matchesBirthday = !birthdayValue || contact.Birthday === birthdayValue;
        const matchesSearch = !searchValue ||
            contact.Contactnumber?.toLowerCase().includes(searchValue) ||
            contact.FirstName?.toLowerCase().includes(searchValue);

        return matchesGroup && matchesWhatsapp && matchesBirthday && matchesSearch;
    });

    // Render table with pagination
    renderTable(filteredContacts);
    renderPagination(filteredContacts);
}

// Render table with pagination
function renderTable(filteredContacts) {
    const itemsPerPage = parseInt(itemsPerPageSelect?.value || 15);
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const paginatedContacts = filteredContacts.slice(start, end);

    contactsTableBody.innerHTML = paginatedContacts.map((contact, index) => {
        // Debug the WhatsApp values
        console.log(`Row ${index + 1}: WhatsApp value = "${contact.WhatsApp}" (type: ${typeof contact.WhatsApp})`);

        const whatsappValue = contact.WhatsApp || 'No';
        const isYes = (contact.WhatsApp === 'Yes' || contact.WhatsApp === 'YES' || contact.WhatsApp === 'yes');
        const cssClass = isYes ? 'whatsapp-yes' : 'whatsapp-no';

        console.log(`Row ${index + 1}: Display="${whatsappValue}", IsYes=${isYes}, CSS Class="${cssClass}"`);

        return `
        <tr data-id="${contact.Serial}">
            <td>
                <input type="checkbox" class="form-check-input row-checkbox" value="${contact.Serial}">
            </td>
            <td>${contact.Serial}</td>
            <td>${contact.ContactName || ''}</td>
            <td>${contact.FirstName || ''}</td>
            <td>${contact.Contactnumber || ''}</td>
            <td>${contact.ContactGroup || ''}</td>
            <td class="whatsapp-cell ${cssClass}" style="position: relative !important; z-index: 999 !important; background: ${isYes ? '#32CD32' : '#FF0000'} !important; background-color: ${isYes ? '#32CD32' : '#FF0000'} !important; background-image: none !important; color: #000000 !important; font-weight: bold !important; text-align: center !important;">
                ${whatsappValue}
            </td>
            <td>${contact.Birthday || ''}</td>
            <td>
                <button class="btn btn-xs btn-primary edit-contact me-1" title="Edit">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-xs btn-danger delete-contact" title="Delete">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        </tr>
        `;
    }).join('');

    // Add event listeners to the new buttons and checkboxes
    addTableButtonListeners();
    addCheckboxListeners();
}

// Render compact pagination
function renderPagination(filteredContacts) {
    const itemsPerPage = parseInt(itemsPerPageSelect?.value || 15);
    const totalPages = Math.ceil(filteredContacts.length / itemsPerPage);
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, filteredContacts.length);

    // Update pagination info
    const paginationInfo = document.getElementById('paginationInfo');
    if (paginationInfo) {
        paginationInfo.textContent = `Showing ${startItem}-${endItem} of ${filteredContacts.length} ${currentDataSource}`;
    }

    // Update current page info
    const currentPageInfo = document.getElementById('currentPageInfo');
    if (currentPageInfo) {
        currentPageInfo.textContent = `${currentPage} / ${totalPages}`;
    }

    // Update prev/next buttons
    const prevPage = document.getElementById('prevPage');
    const nextPage = document.getElementById('nextPage');

    if (prevPage) {
        prevPage.disabled = currentPage === 1;
        prevPage.onclick = () => {
            if (currentPage > 1) {
                currentPage--;
                filterAndDisplayContacts();
            }
        };
    }

    if (nextPage) {
        nextPage.disabled = currentPage === totalPages;
        nextPage.onclick = () => {
            if (currentPage < totalPages) {
                currentPage++;
                filterAndDisplayContacts();
            }
        };
    }
}

// Add event listeners to table buttons
function addTableButtonListeners() {
    document.querySelectorAll('.edit-contact').forEach(button => {
        button.addEventListener('click', (e) => {
            const row = e.target.closest('tr');
            const contact = currentContacts.find(c => c.Serial === parseInt(row.dataset.id));
            openEditModal(contact);
        });
    });

    document.querySelectorAll('.delete-contact').forEach(button => {
        button.addEventListener('click', (e) => {
            const row = e.target.closest('tr');
            if (confirm('Are you sure you want to delete this contact?')) {
                deleteContact(parseInt(row.dataset.id));
            }
        });
    });
}

// Add event listeners to checkboxes
function addCheckboxListeners() {
    // Individual row checkboxes
    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            const row = e.target.closest('tr');
            if (e.target.checked) {
                row.classList.add('row-selected');
            } else {
                row.classList.remove('row-selected');
            }
            updateButtonStates();
        });
    });
}

// Update button states based on selected rows
function updateButtonStates() {
    const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
    const selectedCount = selectedCheckboxes.length;

    // Enable/disable edit button (only for single selection)
    if (editButton) {
        editButton.disabled = selectedCount !== 1;
    }

    // Enable/disable delete button (for one or more selections)
    if (deleteButton) {
        deleteButton.disabled = selectedCount === 0;
    }
}

// Get selected contact data
function getSelectedContact() {
    const selectedCheckbox = document.querySelector('.row-checkbox:checked');
    if (selectedCheckbox) {
        const serialNumber = parseInt(selectedCheckbox.value);
        return currentContacts.find(c => c.Serial === serialNumber);
    }
    return null;
}

// Get all selected contact IDs
function getSelectedContactIds() {
    const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
    return Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));
}

// Open modal for adding/editing contact
function openEditModal(contact = null) {
    const crudModal = new bootstrap.Modal(document.getElementById('crudModal'));
    const saveContactButton = document.getElementById('saveContactButton');

    document.getElementById('crudModalLabel').textContent = contact ? 'Edit Contact' : 'Add Contact';
    document.getElementById('contactId').value = contact?.Serial || '';
    document.getElementById('contactName').value = contact?.ContactName || '';
    document.getElementById('firstName').value = contact?.FirstName || '';
    document.getElementById('contactNumber').value = contact?.Contactnumber || '';
    document.getElementById('contactWhatsApp').value = contact?.WhatsApp || 'No';
    document.getElementById('contactBirthday').value = contact?.Birthday || '';

    // Populate contact group dropdown for modal
    populateModalContactGroups(contact?.ContactGroup || '');

    // Set up the save button event handler
    const existingHandler = saveContactButton.onclick;
    if (existingHandler) {
        saveContactButton.removeEventListener('click', existingHandler);
    }

    saveContactButton.onclick = () => saveContact(contact?.Serial);

    crudModal.show();
}

// Populate contact groups for modal dropdown
function populateModalContactGroups(selectedGroup = '') {
    const contactGroupSelect = document.getElementById('contactGroup');

    // Get unique contact groups from current data
    const groups = [...new Set(currentContacts.map(contact => contact.ContactGroup).filter(Boolean))];

    // Clear existing options
    contactGroupSelect.innerHTML = '';

    // Add default option
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = 'Select a group...';
    contactGroupSelect.appendChild(defaultOption);

    // Add options for each group
    groups.forEach(group => {
        const option = document.createElement('option');
        option.value = group;
        option.textContent = group;
        if (group === selectedGroup) {
            option.selected = true;
        }
        contactGroupSelect.appendChild(option);
    });

    // Add common groups if they don't exist
    const commonGroups = ['Alpha', 'Beta', 'Gamma', 'Delta', 'Leaders', 'Admins'];
    commonGroups.forEach(group => {
        if (!groups.includes(group)) {
            const option = document.createElement('option');
            option.value = group;
            option.textContent = group;
            if (group === selectedGroup) {
                option.selected = true;
            }
            contactGroupSelect.appendChild(option);
        }
    });
}

// Save contact (create or update) using B4J REST API
async function saveContact(serialNumber = null) {
    const contactData = {
        FirstName: document.getElementById('firstName').value,
        Contactnumber: document.getElementById('contactNumber').value,
        ContactGroup: document.getElementById('contactGroup').value,
        WhatsApp: document.getElementById('contactWhatsApp').value,
        Birthday: document.getElementById('contactBirthday').value,
        ContactName: document.getElementById('contactName').value || `${document.getElementById('contactGroup').value} - ${document.getElementById('firstName').value}`,
        Active: "0",
        ContactPhoneType: "mobile",
        ContacteMail: "",
        ContacteMailType: "",
        ContactImage: "(Bitmap) Not initialized",
        Age: null,
        Notes: "",
        Groep: null
    };

    try {
        console.log(`Saving contact data to B4J API...`);

        let apiUrl, method;

        if (serialNumber) {
            // Update existing contact
            apiUrl = `${API_BASE_URL}/contacts/${serialNumber}`;
            method = 'PUT';
        } else {
            // Create new contact
            apiUrl = `${API_BASE_URL}/contacts`;
            method = 'POST';
        }

        console.log(`${method} request to:`, apiUrl);
        console.log('Contact data:', contactData);

        const response = await fetch(apiUrl, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(contactData)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
        }

        const result = await response.json();
        console.log('Save response from B4J API:', result);

        bootstrap.Modal.getInstance(document.getElementById('crudModal')).hide();
        await fetchContacts(); // Refresh the table
        alert(`Contact ${serialNumber ? 'updated' : 'added'} successfully!`);
    } catch (error) {
        console.error('Error saving contact to B4J API:', error);
        alert('Error saving contact to database: ' + error.message);
    }
}

// Delete contact using B4J REST API
async function deleteContact(serialNumber) {
    try {
        console.log(`Deleting contact with serial number: ${serialNumber}`);

        const apiUrl = `${API_BASE_URL}/contacts/${serialNumber}`;
        console.log('DELETE request to:', apiUrl);

        const response = await fetch(apiUrl, {
            method: 'DELETE',
            headers: {
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
        }

        const result = await response.json();
        console.log('Delete response from B4J API:', result);

        await fetchContacts(); // Refresh the table
        alert('Contact deleted successfully!');
    } catch (error) {
        console.error('Error deleting contact from B4J API:', error);
        alert('Error deleting contact from database: ' + error.message);
    }
}

// Delete multiple contacts
async function deleteMultipleContacts(serialNumbers) {
    try {
        console.log(`Deleting contacts with serial numbers: ${serialNumbers.join(', ')}`);

        // Delete contacts one by one (you could also implement batch delete in B4J)
        for (const serialNumber of serialNumbers) {
            const apiUrl = `${API_BASE_URL}/contacts/${serialNumber}`;
            console.log('DELETE request to:', apiUrl);

            const response = await fetch(apiUrl, {
                method: 'DELETE',
                headers: {
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
            }
        }

        await fetchContacts(); // Refresh the table
        alert(`${serialNumbers.length} contact(s) deleted successfully!`);
    } catch (error) {
        console.error('Error deleting contacts from B4J API:', error);
        alert('Error deleting contacts from database: ' + error.message);
    }
}

// Add a variable to track the current data source
let currentDataSource = 'contacts'; // Can be 'contacts' or 'brothers'

// Event Listeners
if (loginForm) {
    loginForm.addEventListener('submit', login);
}

if (logoutButton) {
    logoutButton.addEventListener('click', (e) => {
        e.preventDefault();
        logout();
    });
}

// Add event listener for List All Brothers link
document.getElementById('listAllBrothersLink')?.addEventListener('click', (e) => {
    e.preventDefault();
    fetchBrothers();
});

searchButton?.addEventListener('click', filterAndDisplayContacts);
searchInput?.addEventListener('keyup', (e) => {
    if (e.key === 'Enter') filterAndDisplayContacts();
});
groupFilter?.addEventListener('change', filterAndDisplayContacts);
whatsappFilter?.addEventListener('change', filterAndDisplayContacts);
birthdayFilter?.addEventListener('change', filterAndDisplayContacts);
itemsPerPageSelect?.addEventListener('change', () => {
    currentPage = 1; // Reset to first page when changing items per page
    filterAndDisplayContacts();
});

// Select All checkbox functionality
selectAllCheckbox?.addEventListener('change', (e) => {
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    rowCheckboxes.forEach(checkbox => {
        checkbox.checked = e.target.checked;
        const row = checkbox.closest('tr');
        if (e.target.checked) {
            row.classList.add('row-selected');
        } else {
            row.classList.remove('row-selected');
        }
    });
    updateButtonStates();
});

// CRUD button event listeners
addButton?.addEventListener('click', () => openEditModal());

editButton?.addEventListener('click', () => {
    const selectedContact = getSelectedContact();
    if (selectedContact) {
        openEditModal(selectedContact);
    }
});

deleteButton?.addEventListener('click', () => {
    const selectedIds = getSelectedContactIds();
    if (selectedIds.length > 0) {
        const message = selectedIds.length === 1
            ? 'Are you sure you want to delete this contact?'
            : `Are you sure you want to delete ${selectedIds.length} contacts?`;

        if (confirm(message)) {
            deleteMultipleContacts(selectedIds);
        }
    }
});

// Navigation event listeners
document.querySelector('a[href="#dashboard"]')?.addEventListener('click', (e) => {
    e.preventDefault();
    loadDashboard();
});

listAllBrothersLink?.addEventListener('click', (e) => {
    e.preventDefault();
    showBrothersList();
});

// Logout functionality
logoutButton?.addEventListener('click', (e) => {
    e.preventDefault();
    logout();
});

// Initial load
document.addEventListener('DOMContentLoaded', () => {
    // Set current date in the top nav bar
    const currentDateElement = document.getElementById('currentDate');
    if (currentDateElement) {
        const today = new Date();
        const options = { weekday: 'long', year: 'numeric', month: 'short', day: 'numeric' };
        currentDateElement.textContent = today.toLocaleDateString('en-US', options);
    }

    // Show live system message in message area
    const messageArea = document.getElementById('messageArea');
    if (messageArea) {
        messageArea.textContent = 'Live System - Connected to B4J REST API Server (Login Bypassed)';
    }

    console.log('Running in LIVE MODE with B4J REST API Server - Login Bypassed for Testing');

    // Bypass login and show main application with dashboard
    showMainApplication();

    // Load dashboard by default
    loadDashboard();
});

// ===== QUICK MESSAGE SYSTEM =====

// Quick Message Variables
let selectedRecipients = [];
let currentMessageType = 'text';
let birthdayTemplates = {
    formal: "Dear {name},\n\nOn behalf of the Brotherhood of 555, I would like to extend our warmest birthday wishes to you. May this special day bring you joy, happiness, and continued blessings.\n\nBest regards,\nBrotherhood of 555",
    friendly: "Hey {name}! 🎉\n\nHappy Birthday, brother! Hope you have an amazing day filled with joy, laughter, and all your favorite things. Here's to another year of brotherhood and friendship!\n\nCheers! 🥳",
    brotherhood: "Brother {name}, 🎂\n\nToday we celebrate not just your birthday, but the incredible brother you are to all of us in the 555 family. Your dedication, friendship, and spirit make our brotherhood stronger.\n\nWishing you a fantastic year ahead!\n\nWith brotherhood love,\nThe 555 Family 💪",
    custom: "Happy Birthday {name}! 🎉\n\nWishing you all the best on your special day.\n\nBest wishes,\nBrotherhood of 555"
};

// Initialize Quick Message System
function initializeQuickMessage() {
    console.log('Initializing Quick Message system...');

    // Populate groups dropdown
    populateGroupsDropdown();

    // Set up event listeners
    setupQuickMessageEventListeners();

    // Initialize with WhatsApp verified filter
    updateRecipientSelection();

    // Update message preview
    updateMessagePreview();
}

// Populate groups dropdown
function populateGroupsDropdown() {
    const groupSelect = document.getElementById('groupSelect');
    if (!groupSelect) return;

    // Get unique groups from current contacts
    const groups = [...new Set(currentContacts.map(contact => contact.ContactGroup).filter(Boolean))];

    // Clear existing options except first
    groupSelect.innerHTML = '<option value="">Choose a group...</option>';

    // Add group options
    groups.forEach(group => {
        const option = document.createElement('option');
        option.value = group;
        option.textContent = group;
        groupSelect.appendChild(option);
    });
}

// Set up event listeners for Quick Message
function setupQuickMessageEventListeners() {
    // Recipient filter radio buttons
    document.querySelectorAll('input[name="recipientFilter"]').forEach(radio => {
        radio.addEventListener('change', handleRecipientFilterChange);
    });

    // Group selection
    const groupSelect = document.getElementById('groupSelect');
    if (groupSelect) {
        groupSelect.addEventListener('change', updateRecipientSelection);
    }

    // Message type tabs
    document.querySelectorAll('#messageTypeTabs button').forEach(tab => {
        tab.addEventListener('click', handleMessageTypeChange);
    });

    // Text message input
    const textMessage = document.getElementById('textMessage');
    if (textMessage) {
        textMessage.addEventListener('input', () => {
            updateCharacterCount();
            updateMessagePreview();
        });
    }

    // Birthday template selection
    const birthdayTemplate = document.getElementById('birthdayTemplate');
    if (birthdayTemplate) {
        birthdayTemplate.addEventListener('change', handleBirthdayTemplateChange);
    }

    // Birthday message input
    const birthdayMessage = document.getElementById('birthdayMessage');
    if (birthdayMessage) {
        birthdayMessage.addEventListener('input', updateMessagePreview);
    }

    // Image file input
    const imageFile = document.getElementById('imageFile');
    if (imageFile) {
        imageFile.addEventListener('change', handleImageFileChange);
    }

    // Image caption
    const imageCaption = document.getElementById('imageCaption');
    if (imageCaption) {
        imageCaption.addEventListener('input', updateMessagePreview);
    }

    // Video file input
    const videoFile = document.getElementById('videoFile');
    if (videoFile) {
        videoFile.addEventListener('change', handleVideoFileChange);
    }

    // Video caption
    const videoCaption = document.getElementById('videoCaption');
    if (videoCaption) {
        videoCaption.addEventListener('input', updateMessagePreview);
    }

    // Location inputs
    ['locationName', 'locationAddress', 'locationLat', 'locationLng'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', updateMessagePreview);
        }
    });

    // Send message button
    const sendMessageBtn = document.getElementById('sendMessageBtn');
    if (sendMessageBtn) {
        sendMessageBtn.addEventListener('click', sendWhatsAppMessage);
    }

    // Test message button
    const testMessageBtn = document.getElementById('testMessageBtn');
    if (testMessageBtn) {
        testMessageBtn.addEventListener('click', testMessage);
    }

    // Save draft button
    const saveDraftBtn = document.getElementById('saveDraftBtn');
    if (saveDraftBtn) {
        saveDraftBtn.addEventListener('click', saveDraft);
    }
}

// Handle recipient filter change
function handleRecipientFilterChange(event) {
    const filterType = event.target.value;

    // Show/hide relevant sections
    const groupSelectionRow = document.getElementById('groupSelectionRow');
    const customSelectionRow = document.getElementById('customSelectionRow');

    if (groupSelectionRow) {
        groupSelectionRow.classList.toggle('d-none', filterType !== 'group');
    }

    if (customSelectionRow) {
        customSelectionRow.classList.toggle('d-none', filterType !== 'custom');
    }

    // Update recipient selection
    updateRecipientSelection();
}

// Update recipient selection based on filter
function updateRecipientSelection() {
    const filterType = document.querySelector('input[name="recipientFilter"]:checked')?.value;
    let filteredContacts = [];

    switch (filterType) {
        case 'whatsapp':
            filteredContacts = currentContacts.filter(contact => contact.WhatsApp === 'Yes');
            break;

        case 'group':
            const selectedGroup = document.getElementById('groupSelect')?.value;
            if (selectedGroup) {
                filteredContacts = currentContacts.filter(contact =>
                    contact.ContactGroup === selectedGroup && contact.WhatsApp === 'Yes'
                );
            }
            break;

        case 'birthday':
            const today = new Date();
            const todayStr = today.toISOString().split('T')[0];
            filteredContacts = currentContacts.filter(contact => {
                if (contact.Birthday && contact.WhatsApp === 'Yes') {
                    const birthday = new Date(contact.Birthday);
                    const birthdayThisYear = new Date(today.getFullYear(), birthday.getMonth(), birthday.getDate());
                    return birthdayThisYear.toISOString().split('T')[0] === todayStr;
                }
                return false;
            });
            break;

        case 'custom':
            // Will be handled by individual checkboxes
            populateCustomBrothersList();
            return;
    }

    // Update selected recipients
    selectedRecipients = filteredContacts;
    updateSelectedRecipientsDisplay();
    updateSendButtonState();
}

// Populate custom brothers list with checkboxes
function populateCustomBrothersList() {
    const customBrothersList = document.getElementById('customBrothersList');
    if (!customBrothersList) {
        console.error('customBrothersList element not found');
        return;
    }

    console.log('Total contacts available:', currentContacts.length);
    const whatsappContacts = currentContacts.filter(contact => contact.WhatsApp === 'Yes');
    console.log('WhatsApp verified contacts:', whatsappContacts.length);

    if (whatsappContacts.length === 0) {
        customBrothersList.innerHTML = '<div class="text-muted">No WhatsApp verified contacts found. Please ensure contacts are loaded.</div>';
        return;
    }

    customBrothersList.innerHTML = whatsappContacts.map(contact => `
        <div class="form-check">
            <input class="form-check-input" type="checkbox" value="${contact.Serial}" id="brother_${contact.Serial}" onchange="handleCustomBrotherSelection()">
            <label class="form-check-label" for="brother_${contact.Serial}">
                ${contact.FirstName || contact.ContactName} (${contact.Contactnumber})
            </label>
        </div>
    `).join('');

    console.log('Custom brothers list populated with', whatsappContacts.length, 'contacts');
}

// Handle custom brother selection
function handleCustomBrotherSelection() {
    const checkedBoxes = document.querySelectorAll('#customBrothersList input[type="checkbox"]:checked');
    const selectedIds = Array.from(checkedBoxes).map(cb => parseInt(cb.value));

    selectedRecipients = currentContacts.filter(contact => selectedIds.includes(contact.Serial));
    updateSelectedRecipientsDisplay();
    updateSendButtonState();
}

// Update selected recipients display
function updateSelectedRecipientsDisplay() {
    const selectedRecipientsDiv = document.getElementById('selectedRecipients');
    const recipientCount = document.getElementById('recipientCount');

    if (!selectedRecipientsDiv || !recipientCount) return;

    recipientCount.textContent = selectedRecipients.length;

    if (selectedRecipients.length === 0) {
        selectedRecipientsDiv.innerHTML = '<small class="text-muted">No recipients selected</small>';
    } else {
        const recipientTags = selectedRecipients.map(contact =>
            `<span class="badge bg-primary me-1 mb-1">${contact.FirstName || contact.ContactName}</span>`
        ).join('');
        selectedRecipientsDiv.innerHTML = recipientTags;
    }
}

// Handle message type change
function handleMessageTypeChange(event) {
    currentMessageType = event.target.id.replace('-tab', '');
    updateMessagePreview();
    updateSendButtonState();
}

// Update character count for text messages
function updateCharacterCount() {
    const textMessage = document.getElementById('textMessage');
    const textCharCount = document.getElementById('textCharCount');

    if (textMessage && textCharCount) {
        textCharCount.textContent = textMessage.value.length;
    }
}

// Handle birthday template change
function handleBirthdayTemplateChange(event) {
    const template = event.target.value;
    const birthdayMessage = document.getElementById('birthdayMessage');

    if (birthdayMessage && birthdayTemplates[template]) {
        birthdayMessage.value = birthdayTemplates[template];
        updateMessagePreview();
    }
}

// Handle image file change
function handleImageFileChange(event) {
    const file = event.target.files[0];
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');

    if (file && imagePreview && previewImg) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            imagePreview.classList.remove('d-none');
            updateMessagePreview();
        };
        reader.readAsDataURL(file);
    }
}

// Handle video file change
function handleVideoFileChange(event) {
    const file = event.target.files[0];
    const videoPreview = document.getElementById('videoPreview');
    const previewVideo = document.getElementById('previewVideo');

    if (file && videoPreview && previewVideo) {
        const url = URL.createObjectURL(file);
        previewVideo.src = url;
        videoPreview.classList.remove('d-none');
        updateMessagePreview();
    }
}

// Update message preview
function updateMessagePreview() {
    const messagePreview = document.getElementById('messagePreview');
    if (!messagePreview) return;

    let previewContent = '';

    switch (currentMessageType) {
        case 'text':
            const textMessage = document.getElementById('textMessage')?.value || '';
            previewContent = textMessage || '<small class="text-muted">Enter your message...</small>';
            break;

        case 'image':
            const imageCaption = document.getElementById('imageCaption')?.value || '';
            const hasImage = document.getElementById('imageFile')?.files.length > 0;
            previewContent = `
                ${hasImage ? '<i class="bi bi-image me-2"></i><strong>Image attached</strong><br>' : '<small class="text-muted">No image selected</small><br>'}
                ${imageCaption || '<small class="text-muted">No caption</small>'}
            `;
            break;

        case 'location':
            const locationName = document.getElementById('locationName')?.value || '';
            const locationAddress = document.getElementById('locationAddress')?.value || '';
            const locationLat = document.getElementById('locationLat')?.value || '';
            const locationLng = document.getElementById('locationLng')?.value || '';

            previewContent = `
                <i class="bi bi-geo-alt me-2"></i><strong>Location</strong><br>
                ${locationName || 'No name'}<br>
                ${locationAddress || 'No address'}<br>
                ${locationLat && locationLng ? `Coordinates: ${locationLat}, ${locationLng}` : 'No coordinates'}
            `;
            break;

        case 'video':
            const videoCaption = document.getElementById('videoCaption')?.value || '';
            const hasVideo = document.getElementById('videoFile')?.files.length > 0;
            previewContent = `
                ${hasVideo ? '<i class="bi bi-camera-video me-2"></i><strong>Video attached</strong><br>' : '<small class="text-muted">No video selected</small><br>'}
                ${videoCaption || '<small class="text-muted">No caption</small>'}
            `;
            break;

        case 'birthday':
            const birthdayMessage = document.getElementById('birthdayMessage')?.value || '';
            const sampleName = selectedRecipients.length > 0 ? selectedRecipients[0].FirstName : 'John';
            previewContent = birthdayMessage.replace(/{name}/g, sampleName) || '<small class="text-muted">Select a template or enter custom message...</small>';
            break;
    }

    messagePreview.innerHTML = previewContent;
}

// Update send button state
function updateSendButtonState() {
    const sendMessageBtn = document.getElementById('sendMessageBtn');
    if (!sendMessageBtn) return;

    const hasRecipients = selectedRecipients.length > 0;
    let hasContent = false;

    switch (currentMessageType) {
        case 'text':
            hasContent = document.getElementById('textMessage')?.value.trim().length > 0;
            break;
        case 'image':
            hasContent = document.getElementById('imageFile')?.files.length > 0;
            break;
        case 'location':
            const locationName = document.getElementById('locationName')?.value.trim();
            const locationLat = document.getElementById('locationLat')?.value.trim();
            const locationLng = document.getElementById('locationLng')?.value.trim();
            hasContent = locationName || (locationLat && locationLng);
            break;
        case 'video':
            hasContent = document.getElementById('videoFile')?.files.length > 0;
            break;
        case 'birthday':
            hasContent = document.getElementById('birthdayMessage')?.value.trim().length > 0;
            break;
    }

    sendMessageBtn.disabled = !(hasRecipients && hasContent);
}

// Insert emoji into text message
function insertEmoji(emoji) {
    const textMessage = document.getElementById('textMessage');
    if (textMessage) {
        const cursorPos = textMessage.selectionStart;
        const textBefore = textMessage.value.substring(0, cursorPos);
        const textAfter = textMessage.value.substring(cursorPos);
        textMessage.value = textBefore + emoji + textAfter;
        textMessage.focus();
        textMessage.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);
        updateCharacterCount();
        updateMessagePreview();
    }
}

// Get current location
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                document.getElementById('locationLat').value = position.coords.latitude.toFixed(6);
                document.getElementById('locationLng').value = position.coords.longitude.toFixed(6);
                updateMessagePreview();
                updateSendButtonState();
            },
            function(error) {
                alert('Error getting location: ' + error.message);
            }
        );
    } else {
        alert('Geolocation is not supported by this browser.');
    }
}

// Send WhatsApp message
async function sendWhatsAppMessage() {
    if (selectedRecipients.length === 0) {
        alert('Please select recipients first.');
        return;
    }

    // Show progress
    const sendingProgress = document.getElementById('sendingProgress');
    const sendProgressBar = document.getElementById('sendProgressBar');
    const sendStatus = document.getElementById('sendStatus');

    if (sendingProgress) sendingProgress.classList.remove('d-none');

    try {
        // Prepare message data
        const messageData = await prepareMessageData();

        // Send to each recipient
        let successCount = 0;
        let failCount = 0;

        for (let i = 0; i < selectedRecipients.length; i++) {
            const recipient = selectedRecipients[i];

            // Update progress
            const progress = ((i + 1) / selectedRecipients.length) * 100;
            if (sendProgressBar) sendProgressBar.style.width = progress + '%';
            if (sendStatus) sendStatus.innerHTML = `Sending to ${recipient.FirstName || recipient.ContactName}... (${i + 1}/${selectedRecipients.length})`;

            try {
                // Personalize message for this recipient
                const personalizedMessage = personalizeMessage(messageData, recipient);

                // Send via Green API (Primary) with Smithery.AI fallback
                try {
                    await sendToGreenAPI(recipient, personalizedMessage);
                } catch (greenError) {
                    console.warn('Green API failed, trying Smithery.AI fallback:', greenError.message);
                    await sendToSmitheryMCP(recipient, personalizedMessage);
                }
                successCount++;

                // Small delay between messages
                await new Promise(resolve => setTimeout(resolve, 1000));

            } catch (error) {
                console.error(`Failed to send to ${recipient.FirstName}:`, error);
                failCount++;
            }
        }

        // Show final status
        if (sendStatus) {
            sendStatus.innerHTML = `
                <div class="alert alert-success">
                    <strong>Sending Complete!</strong><br>
                    ✅ Successful: ${successCount}<br>
                    ${failCount > 0 ? `❌ Failed: ${failCount}` : ''}
                </div>
            `;
        }

        // Update message area
        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = `WhatsApp messages sent: ${successCount} successful, ${failCount} failed`;
        }

    } catch (error) {
        console.error('Error sending messages:', error);
        if (sendStatus) {
            sendStatus.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
        }
    }
}

// Prepare message data based on type
async function prepareMessageData() {
    const messageData = {
        type: currentMessageType,
        timestamp: new Date().toISOString()
    };

    switch (currentMessageType) {
        case 'text':
            messageData.text = document.getElementById('textMessage').value;
            break;

        case 'image':
            const imageFile = document.getElementById('imageFile').files[0];
            const imageCaption = document.getElementById('imageCaption').value;
            if (imageFile) {
                messageData.image = await fileToBase64(imageFile);
                messageData.caption = imageCaption;
                messageData.filename = imageFile.name;
            }
            break;

        case 'location':
            messageData.location = {
                name: document.getElementById('locationName').value,
                address: document.getElementById('locationAddress').value,
                latitude: parseFloat(document.getElementById('locationLat').value),
                longitude: parseFloat(document.getElementById('locationLng').value)
            };
            break;

        case 'video':
            const videoFile = document.getElementById('videoFile').files[0];
            const videoCaption = document.getElementById('videoCaption').value;
            if (videoFile) {
                messageData.video = await fileToBase64(videoFile);
                messageData.caption = videoCaption;
                messageData.filename = videoFile.name;
            }
            break;

        case 'birthday':
            messageData.text = document.getElementById('birthdayMessage').value;
            messageData.template = 'birthday';
            break;
    }

    return messageData;
}

// Personalize message for recipient
function personalizeMessage(messageData, recipient) {
    const personalizedData = { ...messageData };

    if (personalizedData.text) {
        personalizedData.text = personalizedData.text.replace(/{name}/g, recipient.FirstName || recipient.ContactName);
    }

    if (personalizedData.caption) {
        personalizedData.caption = personalizedData.caption.replace(/{name}/g, recipient.FirstName || recipient.ContactName);
    }

    return personalizedData;
}

// Green API Configuration
const GREEN_API_CONFIG = {
    apiUrl: 'https://7103.api.greenapi.com',
    mediaUrl: 'https://7103.media.greenapi.com',
    idInstance: '7103251786',
    apiTokenInstance: '047c85965206424dabd1b06fcda50307ab6f1fd73cc94cceb3',
    phone: '27820511229', // Updated to your current WhatsApp number
    testMode: true // SAFETY: Set to false only when ready for production
};

// Send message via Green API (Primary Method)
async function sendToGreenAPI(recipient, messageData) {
    console.log('🟢 Green API - Sending WhatsApp message:', {
        recipient: recipient.Contactnumber,
        messageData: messageData,
        testMode: GREEN_API_CONFIG.testMode
    });

    try {
        // SAFETY CHECK: Only send to WhatsApp verified contacts
        if (recipient.WhatsApp !== 'Yes') {
            console.warn('🛡️ WHATSAPP VERIFICATION: Blocking message to', recipient.Contactnumber, '- WhatsApp not verified (', recipient.WhatsApp, ')');
            return {
                success: false,
                error: 'WhatsApp not verified',
                messageId: 'blocked_not_verified_' + Date.now(),
                recipient: recipient.Contactnumber,
                timestamp: new Date().toISOString(),
                blocked: true,
                reason: 'WhatsApp verification required'
            };
        }

        // Format phone number for Green API (remove + and spaces)
        let phoneNumber = recipient.Contactnumber.replace(/[\s\-\+]/g, '');
        if (!phoneNumber.startsWith('27') && phoneNumber.startsWith('0')) {
            phoneNumber = '27' + phoneNumber.substring(1);
        }

        // Add @c.us suffix for WhatsApp format
        const chatId = phoneNumber + '@c.us';

        // SAFETY CHECK: In test mode, only send to your number
        if (GREEN_API_CONFIG.testMode && phoneNumber !== GREEN_API_CONFIG.phone.replace(/[\s\-\+]/g, '')) {
            console.warn('🛡️ TEST MODE: Blocking message to', phoneNumber, '- Only sending to', GREEN_API_CONFIG.phone);
            return {
                success: true,
                messageId: 'test_blocked_' + Date.now(),
                recipient: phoneNumber,
                timestamp: new Date().toISOString(),
                testMode: true,
                blocked: true
            };
        }

        // Prepare message based on type
        let apiEndpoint = '';
        let requestBody = {};

        switch (messageData.type) {
            case 'text':
            case 'birthday':
                apiEndpoint = 'sendMessage';
                requestBody = {
                    chatId: chatId,
                    message: messageData.text
                };
                break;

            case 'location':
                apiEndpoint = 'sendLocation';
                requestBody = {
                    chatId: chatId,
                    latitude: messageData.location.latitude,
                    longitude: messageData.location.longitude,
                    nameLocation: messageData.location.name,
                    address: messageData.location.address
                };
                break;

            case 'image':
                if (messageData.image) {
                    apiEndpoint = 'sendFileByUpload';
                    // For now, send as text with caption
                    apiEndpoint = 'sendMessage';
                    requestBody = {
                        chatId: chatId,
                        message: `📷 Image: ${messageData.caption || 'Image from Brotherhood 555'}`
                    };
                }
                break;

            case 'video':
                if (messageData.video) {
                    apiEndpoint = 'sendFileByUpload';
                    // For now, send as text with caption
                    apiEndpoint = 'sendMessage';
                    requestBody = {
                        chatId: chatId,
                        message: `🎥 Video: ${messageData.caption || 'Video from Brotherhood 555'}`
                    };
                }
                break;

            default:
                apiEndpoint = 'sendMessage';
                requestBody = {
                    chatId: chatId,
                    message: messageData.text || 'Message from Brotherhood of 555'
                };
        }

        // Make API call to Green API
        const apiUrl = `${GREEN_API_CONFIG.apiUrl}/waInstance${GREEN_API_CONFIG.idInstance}/${apiEndpoint}/${GREEN_API_CONFIG.apiTokenInstance}`;

        console.log('🟢 Green API Request:', {
            url: apiUrl,
            body: requestBody
        });

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Green API Error ${response.status}: ${errorText}`);
        }

        const result = await response.json();
        console.log('🟢 Green API Response:', result);

        return {
            success: true,
            messageId: result.idMessage || 'green_' + Date.now(),
            recipient: phoneNumber,
            chatId: chatId,
            timestamp: new Date().toISOString(),
            testMode: GREEN_API_CONFIG.testMode
        };

    } catch (error) {
        console.error('🟢 Green API Error:', error);
        throw error;
    }
}

// Send message via Smithery.AI MCP server (Fallback)
async function sendToSmitheryMCP(recipient, messageData) {
    console.log('Sending to Smithery.AI WhatsApp MCP:', {
        recipient: recipient.Contactnumber,
        messageData: messageData
    });

    try {
        // Smithery.AI MCP Server Configuration
        const smitheryConfig = {
            serverUrl: 'https://server.smithery.ai/@jlucaso1/whatsapp-mcp-ts',
            apiKey: '3ad51e36-8ddb-4ff2-9c9c-4d220766d696'
        };

        // Prepare the message based on type
        let messageContent = '';
        let messageType = 'text';

        switch (messageData.type) {
            case 'text':
            case 'birthday':
                messageContent = messageData.text;
                messageType = 'text';
                break;

            case 'image':
                messageContent = messageData.caption || 'Image message';
                messageType = 'image';
                break;

            case 'video':
                messageContent = messageData.caption || 'Video message';
                messageType = 'video';
                break;

            case 'location':
                messageContent = `📍 ${messageData.location.name}\n${messageData.location.address}\nCoordinates: ${messageData.location.latitude}, ${messageData.location.longitude}`;
                messageType = 'text';
                break;

            default:
                messageContent = messageData.text || 'Message from Brotherhood of 555';
                messageType = 'text';
        }

        // Format phone number (ensure it starts with country code)
        let phoneNumber = recipient.Contactnumber;
        if (!phoneNumber.startsWith('+')) {
            // Assume South African number if no country code
            phoneNumber = '+27' + phoneNumber.replace(/^0/, '');
        }

        // Try different Smithery.AI MCP endpoints and authentication methods
        const endpoints = [
            // Method 1: Direct tool call with API key in URL
            {
                url: `${smitheryConfig.serverUrl}/tools/send_message?api_key=${smitheryConfig.apiKey}`,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    to: phoneNumber,
                    message: messageContent,
                    type: messageType
                })
            },
            // Method 2: MCP protocol with API key in header
            {
                url: `${smitheryConfig.serverUrl}/mcp`,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': smitheryConfig.apiKey,
                    'X-MCP-Tool': 'send_message'
                },
                body: JSON.stringify({
                    tool: 'send_message',
                    arguments: {
                        to: phoneNumber,
                        message: messageContent,
                        type: messageType
                    }
                })
            },
            // Method 3: Standard API call
            {
                url: `${smitheryConfig.serverUrl}/api/send`,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${smitheryConfig.apiKey}`
                },
                body: JSON.stringify({
                    to: phoneNumber,
                    message: messageContent,
                    type: messageType
                })
            }
        ];

        let lastError = null;

        // Try each endpoint method
        for (let i = 0; i < endpoints.length; i++) {
            const endpoint = endpoints[i];
            console.log(`Trying Smithery.AI method ${i + 1}:`, endpoint.url);

            try {
                const response = await fetch(endpoint.url, {
                    method: endpoint.method,
                    headers: endpoint.headers,
                    body: endpoint.body
                });

                console.log(`Method ${i + 1} Response Status:`, response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log(`Method ${i + 1} Success:`, result);

                    return {
                        success: true,
                        messageId: result.messageId || result.id || 'success_' + Date.now(),
                        recipient: phoneNumber,
                        timestamp: new Date().toISOString(),
                        method: i + 1
                    };
                } else {
                    const errorText = await response.text();
                    console.warn(`Method ${i + 1} failed (${response.status}):`, errorText);
                    lastError = new Error(`Method ${i + 1}: ${response.status} ${errorText}`);
                }

            } catch (error) {
                console.warn(`Method ${i + 1} network error:`, error.message);
                lastError = error;
            }
        }

        // If all methods failed, throw the last error
        throw lastError || new Error('All Smithery.AI connection methods failed');

    } catch (error) {
        console.error('Smithery MCP Error:', error);

        // For development/testing - simulate success
        if (error.message.includes('fetch')) {
            console.warn('Network error - simulating success for testing');
            return {
                success: true,
                messageId: 'simulated_' + Date.now(),
                recipient: recipient.Contactnumber,
                timestamp: new Date().toISOString(),
                simulated: true
            };
        }

        throw error;
    }
}

// Convert file to base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}

// Test message function
async function testMessage() {
    try {
        // Test with a simple text message to your WhatsApp contact
        const whatsappContacts = currentContacts.filter(contact => contact.WhatsApp === 'Yes');

        if (whatsappContacts.length === 0) {
            alert('No WhatsApp verified contacts available for testing');
            return;
        }

        // Try to find your contact first, otherwise use first WhatsApp contact
        let testRecipient = whatsappContacts.find(contact =>
            contact.Contactnumber && contact.Contactnumber.replace(/[\s\-\+]/g, '').includes('27820511229')
        );

        if (!testRecipient) {
            testRecipient = whatsappContacts[0];
            console.warn('Could not find your contact (27820511229), using first WhatsApp contact:', testRecipient.Contactnumber);
        }
        const testMessage = {
            type: 'text',
            text: '🧪 Test message from Brotherhood of 555 Quick Message system',
            timestamp: new Date().toISOString()
        };

        console.log('Testing Smithery.AI MCP with:', testRecipient.FirstName);

        // Show progress
        const sendStatus = document.getElementById('sendStatus');
        if (sendStatus) {
            sendStatus.innerHTML = `<div class="alert alert-info">Testing connection to ${testRecipient.FirstName}...</div>`;
        }

        // Try Green API first, fallback to Smithery.AI
        let result;
        try {
            result = await sendToGreenAPI(testRecipient, testMessage);
        } catch (greenError) {
            console.warn('Green API test failed, trying Smithery.AI:', greenError.message);
            result = await sendToSmitheryMCP(testRecipient, testMessage);
        }

        if (result.success) {
            const message = result.simulated
                ? `✅ Test successful (simulated)!\n\nWould send to: ${testRecipient.FirstName} (${result.recipient})\nMessage ID: ${result.messageId}`
                : `✅ Test message sent successfully!\n\nSent to: ${testRecipient.FirstName} (${result.recipient})\nMessage ID: ${result.messageId}`;

            alert(message);

            if (sendStatus) {
                sendStatus.innerHTML = `<div class="alert alert-success">Test successful! ${result.simulated ? '(Simulated)' : ''}</div>`;
            }
        } else {
            throw new Error('Test failed - no success response');
        }

    } catch (error) {
        console.error('Test message error:', error);
        alert(`❌ Test failed: ${error.message}`);

        const sendStatus = document.getElementById('sendStatus');
        if (sendStatus) {
            sendStatus.innerHTML = `<div class="alert alert-danger">Test failed: ${error.message}</div>`;
        }
    }
}

// Save draft function
function saveDraft() {
    const draftData = {
        messageType: currentMessageType,
        recipients: selectedRecipients.map(r => r.Serial),
        timestamp: new Date().toISOString()
    };

    // Add message content based on type
    switch (currentMessageType) {
        case 'text':
            draftData.text = document.getElementById('textMessage').value;
            break;
        case 'birthday':
            draftData.text = document.getElementById('birthdayMessage').value;
            break;
        // Add other types as needed
    }

    // Save to localStorage for now
    const drafts = JSON.parse(localStorage.getItem('whatsappDrafts') || '[]');
    drafts.push(draftData);
    localStorage.setItem('whatsappDrafts', JSON.stringify(drafts));

    alert('Draft saved successfully!');
}

// Add navigation event listener for Quick Message
document.querySelector('a[href="#quick-message"]')?.addEventListener('click', (e) => {
    e.preventDefault();
    showQuickMessage();
});

// ===== DATA CLEANSE FORM SYSTEM =====

// Data Cleanse Form Configuration
const DATA_CLEANSE_CONFIG = {
    formTitle: "Brotherhood of 555 - Data Update",
    subtitle: "Your Story - HIS Glory",
    message: "Ons by 555 is besig om ons kontaklys skoon te maak en op te dateer.\nIndien jy nie reageer het sal ons jou steeds die vorm in en kies die \"Aan Beweeg Groep\" - dit sal beteken wees ons jy te doen gaan - maak ons asseblief. Ons sal jou dan afhaal van ons lys.",
    groups: [
        "Green Olive",
        "ZMerino",
        "ZuiderGrief",
        "Red Truck",
        "Florauua Bakery"
    ],
    webFormUrl: "https://indexbuddy.co.za/manne/form", // Your B4J server form endpoint
    responseWebhook: "https://indexbuddy.co.za/manne/webhook" // Your B4J webhook endpoint
};

// Generate personalized data cleanse message for WhatsApp
function generateDataCleanseMessage(contact) {

    // Format birthday for display
    let birthdayDisplay = 'Nie beskikbaar';
    if (contact.Birthday) {
        try {
            const birthday = new Date(contact.Birthday);
            birthdayDisplay = birthday.toLocaleDateString('af-ZA');
        } catch (e) {
            birthdayDisplay = contact.Birthday;
        }
    }

    // Create personalized form URL with pre-filled data
    const formParams = new URLSearchParams({
        serial: contact.Serial || '',
        name: contact.ContactName || '',
        firstname: contact.FirstName || '',
        email: contact.Email || '',
        phone: contact.Contactnumber || '',
        birthday: contact.Birthday || '',
        group: contact.ContactGroup || '',
        whatsapp: contact.WhatsApp || 'No'
    });

    const personalizedFormUrl = `${DATA_CLEANSE_CONFIG.webFormUrl}?${formParams.toString()}`;

    // Create WhatsApp message
    const message = `🔴⚪ *BROTHERHOOD OF 555* ⚪🔴
*Your Story - HIS Glory*

Hallo *${contact.FirstName || contact.ContactName}*! 👋

${DATA_CLEANSE_CONFIG.message}

📋 *JOU HUIDIGE BESONDERHEDE:*
👤 Naam: ${contact.ContactName || 'Nie beskikbaar'}
📱 Sel: ${contact.Contactnumber || 'Nie beskikbaar'}
📧 Epos: ${contact.Email || 'Nie beskikbaar'}
🎂 Geboorte: ${birthdayDisplay}
👥 Groep: ${contact.ContactGroup || 'Nie beskikbaar'}
📲 WhatsApp: ${contact.WhatsApp || 'No'}

🔄 *AKSIES BESKIKBAAR:*

1️⃣ *ALLES KORREK* - Stuur "KORREK"
2️⃣ *UPDATE NODIG* - Klik hierdie skakel:
${personalizedFormUrl}

⏰ *Reageer asseblief binne 7 dae*

Baie dankie! 🙏
Brotherhood of 555`;

    return {
        message: message,
        formUrl: personalizedFormUrl,
        contact: contact
    };
}

// Send data cleanse forms to all WhatsApp verified contacts
async function sendDataCleanseForms() {
    // Safety confirmation
    const whatsappContacts = currentContacts.filter(contact => contact.WhatsApp === 'Yes');

    if (whatsappContacts.length === 0) {
        alert('Geen WhatsApp geverifieerde kontakte gevind nie.');
        return;
    }

    const confirmMessage = `🔄 DATA CLEANSE FORMS

Gereed om data skoonmaak vorms te stuur na ${whatsappContacts.length} WhatsApp geverifieerde Brothers.

${GREEN_API_CONFIG.testMode ? '🛡️ TEST MODE: Sal slegs na jou nommer stuur (27820511229)' : '⚠️ PRODUCTION MODE: Sal na alle Brothers stuur'}

Wil jy voortgaan?`;

    if (!confirm(confirmMessage)) {
        return;
    }

    // Show progress
    const sendingProgress = document.getElementById('sendingProgress');
    const sendProgressBar = document.getElementById('sendProgressBar');
    const sendStatus = document.getElementById('sendStatus');

    if (sendingProgress) sendingProgress.classList.remove('d-none');

    try {
        let successCount = 0;
        let failCount = 0;
        let blockedCount = 0;

        for (let i = 0; i < whatsappContacts.length; i++) {
            const contact = whatsappContacts[i];

            // Update progress
            const progress = ((i + 1) / whatsappContacts.length) * 100;
            if (sendProgressBar) sendProgressBar.style.width = progress + '%';
            if (sendStatus) sendStatus.innerHTML = `Stuur data vorm na ${contact.FirstName || contact.ContactName}... (${i + 1}/${whatsappContacts.length})`;

            try {
                // Generate personalized message
                const cleanseData = generateDataCleanseMessage(contact);

                const messageData = {
                    type: 'text',
                    text: cleanseData.message,
                    timestamp: new Date().toISOString(),
                    formUrl: cleanseData.formUrl,
                    purpose: 'data_cleanse'
                };

                // Send via Green API
                const result = await sendToGreenAPI(contact, messageData);

                if (result.success && !result.blocked) {
                    successCount++;
                    console.log(`✅ Data cleanse form sent to ${contact.FirstName}: ${result.messageId}`);
                } else if (result.blocked) {
                    blockedCount++;
                    console.log(`🛡️ Blocked sending to ${contact.FirstName} (${result.reason || 'safety'})`);
                } else {
                    failCount++;
                    console.error(`❌ Failed to send to ${contact.FirstName}`);
                }

                // Delay between messages to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 2000));

            } catch (error) {
                console.error(`Failed to send data cleanse form to ${contact.FirstName}:`, error);
                failCount++;
            }
        }

        // Show final status
        if (sendStatus) {
            sendStatus.innerHTML = `
                <div class="alert alert-success">
                    <strong>Data Cleanse Forms Versending Voltooi!</strong><br>
                    ✅ Suksesvol: ${successCount}<br>
                    ${blockedCount > 0 ? `🛡️ Geblokkeer (Test Mode): ${blockedCount}<br>` : ''}
                    ${failCount > 0 ? `❌ Gefaal: ${failCount}` : ''}
                </div>
            `;
        }

        // Update message area
        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = `Data cleanse forms sent: ${successCount} successful, ${failCount} failed, ${blockedCount} blocked`;
        }

    } catch (error) {
        console.error('Error sending data cleanse forms:', error);
        if (sendStatus) {
            sendStatus.innerHTML = `<div class="alert alert-danger">Fout: ${error.message}</div>`;
        }
    }
}

// Test data cleanse form generation
function testDataCleanseForm() {
    const whatsappContacts = currentContacts.filter(contact => contact.WhatsApp === 'Yes');

    if (whatsappContacts.length === 0) {
        alert('Geen WhatsApp kontakte beskikbaar vir toets nie');
        return;
    }

    // Find your contact or use first one
    let testContact = whatsappContacts.find(contact =>
        contact.Contactnumber && contact.Contactnumber.replace(/[\s\-\+]/g, '').includes('27820511229')
    );

    if (!testContact) {
        testContact = whatsappContacts[0];
    }

    const cleanseData = generateDataCleanseMessage(testContact);

    // Show preview
    alert(`📋 DATA CLEANSE FORM PREVIEW:\n\n${cleanseData.message}\n\n🔗 Form URL:\n${cleanseData.formUrl}`);

    console.log('Data Cleanse Form Preview:', cleanseData);
}

// Add data cleanse functions to dashboard quick actions
function showDataCleanse() {
    // For now, show options
    const options = `🔄 DATA CLEANSE SYSTEM

Kies 'n opsie:

1. Test Data Cleanse Form
2. Send Data Cleanse Forms (All Brothers)
3. View Response Statistics

Wat wil jy doen?`;

    const choice = prompt(options + "\n\nTik 1, 2, of 3:");

    switch (choice) {
        case '1':
            testDataCleanseForm();
            break;
        case '2':
            sendDataCleanseForms();
            break;
        case '3':
            alert('Response statistics coming soon!');
            break;
        default:
            if (choice !== null) {
                alert('Ongeldige keuse. Probeer weer.');
            }
    }
}