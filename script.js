// B4J REST API Server Configuration
const API_BASE_URL = 'https://indexbuddy.co.za/manne';

// Current section tracking
let currentSection = 'dashboard';

let currentContacts = [];
let mcpInitialized = false; // Track if MCP server is initialized
let currentPage = 1;

// Create Smithery URL with configuration (similar to createSmitheryUrl from SDK)
function createSmitheryUrl(baseUrl, options) {
    const url = new URL(baseUrl);

    // Add API key
    url.searchParams.set('api_key', options.apiKey);

    // Add configuration as JSON
    if (options.config) {
        url.searchParams.set('config', JSON.stringify(options.config));
    }

    return url.toString();
}

// Parse Server-Sent Events response
function parseSSEResponse(sseText) {
    const lines = sseText.split('\n');
    const events = [];
    let currentEvent = {};

    for (const line of lines) {
        if (line.startsWith('event:')) {
            currentEvent.event = line.substring(6).trim();
        } else if (line.startsWith('data:')) {
            const dataStr = line.substring(5).trim();
            try {
                currentEvent.data = JSON.parse(dataStr);
            } catch (e) {
                currentEvent.data = dataStr;
            }
        } else if (line.trim() === '') {
            // Empty line indicates end of event
            if (currentEvent.event || currentEvent.data) {
                events.push(currentEvent);
                currentEvent = {};
            }
        }
    }

    // Add the last event if it exists
    if (currentEvent.event || currentEvent.data) {
        events.push(currentEvent);
    }

    return events;
}
const rowsPerPage = 10; // Number of rows per page
let authToken = null; // Store authentication token

// DOM Elements - Login
const loginForm = document.getElementById('loginForm');
const loginMessage = document.getElementById('loginMessage');
const loginScreen = document.getElementById('loginScreen');
const mainApplication = document.getElementById('mainApplication');
const logoutButton = document.getElementById('logoutButton');

// DOM Elements - Main App
const contactsTableBody = document.getElementById('contactsTableBody');
const searchInput = document.getElementById('searchInput');
const searchButton = document.getElementById('searchButton');
const groupFilter = document.getElementById('groupFilter');
const whatsappFilter = document.getElementById('whatsappFilter');
const birthdayFilter = document.getElementById('birthdayFilter');
const addButton = document.getElementById('addButton');
const editButton = document.getElementById('editButton');
const deleteButton = document.getElementById('deleteButton');
const selectAllCheckbox = document.getElementById('selectAll');
const itemsPerPageSelect = document.getElementById('itemsPerPage');
const loadingSpinner = document.getElementById('loadingSpinner');

// Login function - simplified for MCP server
async function login(event) {
    event.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    if (!username || !password) {
        showLoginError('Please enter both username and password');
        return;
    }

    try {
        loadingSpinner.style.display = 'block';

        // For MCP server, we'll use a simple authentication approach
        // Store credentials for API calls
        authToken = btoa(`${username}:${password}`); // Base64 encode credentials
        localStorage.setItem('authToken', authToken);
        localStorage.setItem('username', username);

        // Test connection to MCP server
        console.log('Testing MCP connection during login...');
        const testResponse = await testMCPConnection();
        console.log('Test response:', testResponse);

        if (testResponse && testResponse.success) {
            // Show main application
            showMainApplication();

            // Update message in the top bar
            const messageArea = document.getElementById('messageArea');
            if (messageArea) {
                messageArea.textContent = `Welcome, ${username}!`;
            }

            // Fetch contacts from live database
            await fetchContacts();
        } else {
            throw new Error('Authentication failed');
        }
    } catch (error) {
        console.error('Login error:', error);
        showLoginError('Login failed. Please check your credentials and try again.');
    } finally {
        loadingSpinner.style.display = 'none';
    }
}

// Initialize MCP server
async function initializeMCPServer() {
    console.log('initializeMCPServer called, mcpInitialized:', mcpInitialized);

    if (mcpInitialized) {
        console.log('MCP server already initialized, returning success');
        return { success: true };
    }

    try {
        console.log('Starting MCP server initialization...');

        const requestUrl = `${MCP_SERVER_URL}?api_key=${API_KEY}&profile=${PROFILE}`;

        const response = await fetch(requestUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/event-stream'
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                id: 1,
                method: 'initialize',
                params: {
                    protocolVersion: '2024-11-05',
                    capabilities: {
                        tools: {}
                    },
                    clientInfo: {
                        name: 'Contact Management System',
                        version: '1.0.0'
                    }
                }
            })
        });

        if (response.ok) {
            // The response might be Server-Sent Events format, so handle it as text first
            const responseText = await response.text();
            console.log('MCP server initialization response (raw):', responseText);

            try {
                // Try to parse as JSON first
                const data = JSON.parse(responseText);
                console.log('MCP server initialized successfully:', data);
                mcpInitialized = true;
                return { success: true, data };
            } catch (parseError) {
                // If it's not JSON, it might be SSE format - parse the event stream
                console.log('Response is not JSON, parsing as SSE...');
                const sseData = parseSSEResponse(responseText);
                console.log('MCP server initialized successfully (SSE):', sseData);
                mcpInitialized = true;
                return { success: true, data: sseData };
            }
        } else {
            const errorText = await response.text();
            console.error('MCP server initialization failed:', errorText);
            return { success: false, error: errorText };
        }
    } catch (error) {
        console.error('MCP server initialization error:', error);
        return { success: false, error: error.message };
    }
}

// Test MCP server connection - initialize first, then test
async function testMCPConnection() {
    console.log('Testing MCP server connection...');

    // First, initialize the MCP server
    const initResult = await initializeMCPServer();
    if (!initResult.success) {
        console.error('Failed to initialize MCP server:', initResult.error);
        return initResult;
    }

    // Now try calling a tool
    try {
        const testUrl = `${MCP_SERVER_URL}?api_key=${API_KEY}&profile=${PROFILE}`;

        const response = await fetch(testUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/event-stream'
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                id: 2,
                method: 'tools/call',
                params: {
                    name: 'run_sql_query',
                    arguments: {
                        query: 'SHOW TABLES'
                    }
                }
            })
        });

        if (response.ok) {
            const data = await response.json();
            console.log('MCP server connection successful:', data);
            return { success: true, data };
        } else {
            // Try to get the error details from the response
            let errorDetails = '';
            try {
                const errorData = await response.json();
                errorDetails = JSON.stringify(errorData, null, 2);
                console.error('MCP Server Connection Error Response:', errorData);
            } catch (e) {
                const errorText = await response.text();
                errorDetails = errorText;
                console.error('MCP Server Connection Error Text:', errorText);
            }
            console.error('MCP server connection failed:', response.status, errorDetails);
            return { success: false, error: `HTTP ${response.status}: ${errorDetails}` };
        }
    } catch (error) {
        console.error('MCP server connection error:', error);
        return { success: false, error: error.message };
    }
}

// Show login error message
function showLoginError(message) {
    loginMessage.textContent = message;
    loginMessage.classList.remove('d-none');

    // Hide the message after 3 seconds
    setTimeout(() => {
        loginMessage.classList.add('d-none');
    }, 3000);
}

// Show main application
function showMainApplication() {
    loginScreen.classList.add('d-none');
    mainApplication.classList.remove('d-none');
}

// Show login screen
function showLoginScreen() {
    mainApplication.classList.add('d-none');
    loginScreen.classList.remove('d-none');
    document.getElementById('username').value = '';
    document.getElementById('password').value = '';
    loginMessage.classList.add('d-none');
}

// Logout function
function logout() {
    // Clear authentication data
    authToken = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('fingerprint');

    // Show login screen
    showLoginScreen();
}

// Check if user is already logged in - BYPASSED FOR TESTING
function checkAuth() {
    // Bypass login for now - go straight to main application
    console.log('Login bypassed for testing - going straight to main application');

    // Set a temporary auth token
    authToken = 'bypass-token';

    // Show main application directly
    showMainApplication();

    // Update message in the top bar
    const messageArea = document.getElementById('messageArea');
    if (messageArea) {
        messageArea.textContent = 'Welcome! (Login bypassed for testing)';
    }

    // Fetch contacts from live database
    fetchContacts();
}

// Logout function
function logout() {
    // Clear authentication data
    authToken = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('username');

    // Show login screen
    showLoginScreen();
}

// Fetch brothers data from B4J REST API server
async function fetchBrothers() {
    try {
        loadingSpinner.style.display = 'block';

        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = 'Loading brothers from live database...';
        }

        console.log('=== FETCHING BROTHERS FROM B4J API ===');

        const apiUrl = `${API_BASE_URL}/555`;  // Same endpoint for now
        console.log('Brothers API URL:', apiUrl);

        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Raw brothers API response:', data);

        // Process the response data
        if (Array.isArray(data)) {
            currentContacts = data.map((row, index) => ({
                Serial: row.Serial || row.id || index + 1,
                ContactName: row.ContactName || row.name || `Brother - ${row.FirstName || row.firstName || ''}`,
                FirstName: row.FirstName || row.firstName || '',
                Contactnumber: row.Contactnumber || row.phone || row.contactNumber || '',
                ContactGroup: row.ContactGroup || row.group || '',
                WhatsApp: row.WhatsApp || row.whatsapp || 'No',
                Birthday: row.Birthday || row.birthday || ''
            }));
        } else if (data.brothers && Array.isArray(data.brothers)) {
            currentContacts = data.brothers.map((row, index) => ({
                Serial: row.Serial || row.id || index + 1,
                ContactName: row.ContactName || row.name || `Brother - ${row.FirstName || row.firstName || ''}`,
                FirstName: row.FirstName || row.firstName || '',
                Contactnumber: row.Contactnumber || row.phone || row.contactNumber || '',
                ContactGroup: row.ContactGroup || row.group || '',
                WhatsApp: row.WhatsApp || row.whatsapp || 'No',
                Birthday: row.Birthday || row.birthday || ''
            }));
        } else if (data.data && Array.isArray(data.data)) {
            currentContacts = data.data.map((row, index) => ({
                Serial: row.Serial || row.id || index + 1,
                ContactName: row.ContactName || row.name || `Brother - ${row.FirstName || row.firstName || ''}`,
                FirstName: row.FirstName || row.firstName || '',
                Contactnumber: row.Contactnumber || row.phone || row.contactNumber || '',
                ContactGroup: row.ContactGroup || row.group || '',
                WhatsApp: row.WhatsApp || row.whatsapp || 'No',
                Birthday: row.Birthday || row.birthday || ''
            }));
        } else {
            console.warn('Unexpected brothers data format:', data);
            currentContacts = [];
        }

        console.log('Processed brothers:', currentContacts);

        currentDataSource = 'brothers';

        // Update UI
        if (messageArea) {
            messageArea.textContent = `Loaded ${currentContacts.length} brothers from live database`;
        }

        document.querySelector('h1').textContent = 'Brothers List';
        populateContactGroups();
        filterAndDisplayContacts();

        console.log('=== BROTHERS B4J API CONNECTION SUCCESSFUL ===');

    } catch (error) {
        console.error('=== BROTHERS B4J API CONNECTION FAILED ===', error);

        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = 'Failed to load brothers from live database';
        }

        // Log error to console only (no alert popup)
        console.error('Brothers API connection error details logged above');

        throw error;
    } finally {
        loadingSpinner.style.display = 'none';
    }
}

// Dashboard Functions
async function loadDashboard() {
    try {
        console.log('Loading dashboard...');

        // Show dashboard section, hide others
        showSection('dashboard');

        // Update message area
        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = 'Loading dashboard data...';
        }

        // Fetch data for dashboard counts
        const apiUrl = `${API_BASE_URL}/555`;
        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const responseText = await response.text();
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            throw new Error('Invalid JSON response from server');
        }

        // Calculate dashboard statistics
        const totalContacts = data.length;
        const whatsappYes = data.filter(contact => contact.WhatsApp === 'Yes').length;
        const uniqueGroups = [...new Set(data.map(contact => contact.ContactGroup).filter(group => group))].length;

        // Calculate birthdays this month
        const currentMonth = new Date().getMonth() + 1;
        const birthdaysThisMonth = data.filter(contact => {
            if (contact.Birthday) {
                const birthMonth = new Date(contact.Birthday).getMonth() + 1;
                return birthMonth === currentMonth;
            }
            return false;
        }).length;

        // Update dashboard cards
        document.getElementById('totalContactsCount').textContent = totalContacts;
        document.getElementById('whatsappYesCount').textContent = whatsappYes;
        document.getElementById('totalGroupsCount').textContent = uniqueGroups;
        document.getElementById('birthdaysThisMonthCount').textContent = birthdaysThisMonth;

        // Update system status
        document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
        document.getElementById('totalRecords').textContent = totalContacts;

        if (messageArea) {
            messageArea.textContent = 'Dashboard loaded successfully';
        }

        console.log('Dashboard loaded successfully');

    } catch (error) {
        console.error('Error loading dashboard:', error);

        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = 'Error loading dashboard data';
        }

        // Set default values on error
        document.getElementById('totalContactsCount').textContent = '0';
        document.getElementById('whatsappYesCount').textContent = '0';
        document.getElementById('totalGroupsCount').textContent = '0';
        document.getElementById('birthdaysThisMonthCount').textContent = '0';
        document.getElementById('lastUpdate').textContent = 'Error';
        document.getElementById('totalRecords').textContent = '0';
    }
}

// Show specific section and hide others
function showSection(sectionName) {
    // Hide all sections
    document.getElementById('dashboardSection').classList.add('d-none');
    document.getElementById('brothersListSection').classList.add('d-none');

    // Update page title in top nav bar
    const pageTitle = document.getElementById('pageTitle');

    // Show requested section
    if (sectionName === 'dashboard') {
        document.getElementById('dashboardSection').classList.remove('d-none');
        currentSection = 'dashboard';
        if (pageTitle) pageTitle.textContent = 'Dashboard';
    } else if (sectionName === 'brothersList') {
        document.getElementById('brothersListSection').classList.remove('d-none');
        currentSection = 'brothersList';
        if (pageTitle) pageTitle.textContent = 'Brothers List';
    }
}

// Navigation functions for dashboard quick actions
function showBrothersList() {
    showSection('brothersList');
    fetchContacts(); // Load the contacts data
}

function addNewContact() {
    showSection('brothersList');
    // Open the add modal
    setTimeout(() => {
        const addButton = document.getElementById('addButton');
        if (addButton) {
            addButton.click();
        }
    }, 100);
}

function showCommunication() {
    alert('Communication features coming soon!');
}

function showReports() {
    alert('Reports features coming soon!');
}

// Fetch contacts from B4J REST API server
async function fetchContacts() {
    try {
        loadingSpinner.style.display = 'block';

        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = 'Loading contacts from live database...';
        }

        console.log('=== CONNECTING TO B4J REST API ===');

        // Test your B4J server endpoint (matching your B4J code)
        const apiUrl = `${API_BASE_URL}/555`;
        console.log('Testing B4J API URL:', apiUrl);

        let response;
        try {
            // Try direct call first
            response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            });
        } catch (corsError) {
            console.log('Direct call failed due to CORS, trying alternative...');

            // Alternative: Try with different headers
            response = await fetch(apiUrl, {
                method: 'GET',
                mode: 'no-cors'
            });

            if (response.type === 'opaque') {
                throw new Error('CORS_ERROR: Your B4J server needs CORS headers. See console for solution.');
            }
        }

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // First, let's see the raw response text
        const responseText = await response.text();
        console.log('Raw response text from B4J server:', responseText);
        console.log('Response length:', responseText.length);

        // Try to parse as JSON
        let data;
        try {
            data = JSON.parse(responseText);
            console.log('Successfully parsed JSON:', data);
        } catch (parseError) {
            console.error('JSON Parse Error:', parseError);
            console.log('First 500 characters of response:', responseText.substring(0, 500));
            console.log('Last 500 characters of response:', responseText.substring(Math.max(0, responseText.length - 500)));
            throw new Error('Invalid JSON response from server: ' + parseError.message);
        }

        // Process the response data
        if (Array.isArray(data)) {
            // If data is directly an array of contacts
            currentContacts = data.map((row, index) => ({
                Serial: row.Serial || row.id || index + 1,
                ContactName: row.ContactName || row.name || '',
                FirstName: row.FirstName || row.firstName || '',
                Contactnumber: row.Contactnumber || row.phone || row.contactNumber || '',
                ContactGroup: row.ContactGroup || row.group || '',
                WhatsApp: row.WhatsApp || row.whatsapp || 'No',
                Birthday: row.Birthday || row.birthday || ''
            }));
        } else if (data.contacts && Array.isArray(data.contacts)) {
            // If data has a contacts property
            currentContacts = data.contacts.map((row, index) => ({
                Serial: row.Serial || row.id || index + 1,
                ContactName: row.ContactName || row.name || '',
                FirstName: row.FirstName || row.firstName || '',
                Contactnumber: row.Contactnumber || row.phone || row.contactNumber || '',
                ContactGroup: row.ContactGroup || row.group || '',
                WhatsApp: row.WhatsApp || row.whatsapp || 'No',
                Birthday: row.Birthday || row.birthday || ''
            }));
        } else if (data.data && Array.isArray(data.data)) {
            // If data has a data property
            currentContacts = data.data.map((row, index) => ({
                Serial: row.Serial || row.id || index + 1,
                ContactName: row.ContactName || row.name || '',
                FirstName: row.FirstName || row.firstName || '',
                Contactnumber: row.Contactnumber || row.phone || row.contactNumber || '',
                ContactGroup: row.ContactGroup || row.group || '',
                WhatsApp: row.WhatsApp || row.whatsapp || 'No',
                Birthday: row.Birthday || row.birthday || ''
            }));
        } else {
            console.warn('Unexpected data format:', data);
            currentContacts = [];
        }

        console.log('Processed contacts:', currentContacts);

        currentDataSource = 'contacts';

        // Update UI
        if (messageArea) {
            messageArea.textContent = `Loaded ${currentContacts.length} contacts from live database`;
        }

        document.querySelector('h1').textContent = 'Contact Management System';
        populateContactGroups();
        filterAndDisplayContacts();

        console.log('=== B4J API CONNECTION SUCCESSFUL ===');

    } catch (error) {
        console.error('=== B4J API CONNECTION FAILED ===', error);

        const messageArea = document.getElementById('messageArea');
        if (messageArea) {
            messageArea.textContent = 'CORS Error - B4J Server needs configuration';
        }

        // Show detailed CORS solution
        console.log('');
        console.log('🔧 CORS SOLUTION FOR YOUR B4J SERVER:');
        console.log('Add these lines to your B4J server handler:');
        console.log('');
        console.log('resp.SetHeader("Access-Control-Allow-Origin", "*")');
        console.log('resp.SetHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")');
        console.log('resp.SetHeader("Access-Control-Allow-Headers", "Content-Type, Accept")');
        console.log('');
        console.log('If req.Method = "OPTIONS" Then');
        console.log('    resp.SendResponse(200, "OK", "")');
        console.log('    Return');
        console.log('End If');
        console.log('');

        // Log error to console only (no alert popup)
        console.error('Error details logged above for debugging');

        throw error;
    } finally {
        loadingSpinner.style.display = 'none';
    }
}

// Populate contact groups for filter
function populateContactGroups() {
    // Get unique contact groups
    const groups = [...new Set(currentContacts.map(contact => contact.ContactGroup).filter(Boolean))];

    // Clear existing options except the "All" option
    while (groupFilter.options.length > 1) {
        groupFilter.remove(1);
    }

    // Add options for each group
    groups.forEach(group => {
        const option = document.createElement('option');
        option.value = group;
        option.textContent = group;
        groupFilter.appendChild(option);
    });
}

// Filter and display contacts
function filterAndDisplayContacts() {
    let filteredContacts = [...currentContacts];

    // Apply filters
    const groupValue = groupFilter.value.toLowerCase();
    const whatsappValue = whatsappFilter.value;
    const birthdayValue = birthdayFilter.value;
    const searchValue = searchInput.value.toLowerCase();

    filteredContacts = filteredContacts.filter(contact => {
        const matchesGroup = !groupValue || contact.ContactGroup?.toString().toLowerCase().includes(groupValue);
        const matchesWhatsapp = !whatsappValue || contact.WhatsApp === whatsappValue;
        const matchesBirthday = !birthdayValue || contact.Birthday === birthdayValue;
        const matchesSearch = !searchValue ||
            contact.Contactnumber?.toLowerCase().includes(searchValue) ||
            contact.FirstName?.toLowerCase().includes(searchValue);

        return matchesGroup && matchesWhatsapp && matchesBirthday && matchesSearch;
    });

    // Render table with pagination
    renderTable(filteredContacts);
    renderPagination(filteredContacts);
}

// Render table with pagination
function renderTable(filteredContacts) {
    const itemsPerPage = parseInt(itemsPerPageSelect?.value || 15);
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const paginatedContacts = filteredContacts.slice(start, end);

    contactsTableBody.innerHTML = paginatedContacts.map((contact, index) => {
        // Debug the WhatsApp values
        console.log(`Row ${index + 1}: WhatsApp value = "${contact.WhatsApp}" (type: ${typeof contact.WhatsApp})`);

        const whatsappValue = contact.WhatsApp || 'No';
        const isYes = (contact.WhatsApp === 'Yes' || contact.WhatsApp === 'YES' || contact.WhatsApp === 'yes');
        const cssClass = isYes ? 'whatsapp-yes' : 'whatsapp-no';

        console.log(`Row ${index + 1}: Display="${whatsappValue}", IsYes=${isYes}, CSS Class="${cssClass}"`);

        return `
        <tr data-id="${contact.Serial}">
            <td>
                <input type="checkbox" class="form-check-input row-checkbox" value="${contact.Serial}">
            </td>
            <td>${contact.Serial}</td>
            <td>${contact.ContactName || ''}</td>
            <td>${contact.FirstName || ''}</td>
            <td>${contact.Contactnumber || ''}</td>
            <td>${contact.ContactGroup || ''}</td>
            <td class="whatsapp-cell ${cssClass}" style="position: relative !important; z-index: 999 !important; background: ${isYes ? '#32CD32' : '#FF0000'} !important; background-color: ${isYes ? '#32CD32' : '#FF0000'} !important; background-image: none !important; color: #000000 !important; font-weight: bold !important; text-align: center !important;">
                ${whatsappValue}
            </td>
            <td>${contact.Birthday || ''}</td>
            <td>
                <button class="btn btn-xs btn-primary edit-contact me-1" title="Edit">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-xs btn-danger delete-contact" title="Delete">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        </tr>
        `;
    }).join('');

    // Add event listeners to the new buttons and checkboxes
    addTableButtonListeners();
    addCheckboxListeners();
}

// Render compact pagination
function renderPagination(filteredContacts) {
    const itemsPerPage = parseInt(itemsPerPageSelect?.value || 15);
    const totalPages = Math.ceil(filteredContacts.length / itemsPerPage);
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, filteredContacts.length);

    // Update pagination info
    const paginationInfo = document.getElementById('paginationInfo');
    if (paginationInfo) {
        paginationInfo.textContent = `Showing ${startItem}-${endItem} of ${filteredContacts.length} ${currentDataSource}`;
    }

    // Update current page info
    const currentPageInfo = document.getElementById('currentPageInfo');
    if (currentPageInfo) {
        currentPageInfo.textContent = `${currentPage} / ${totalPages}`;
    }

    // Update prev/next buttons
    const prevPage = document.getElementById('prevPage');
    const nextPage = document.getElementById('nextPage');

    if (prevPage) {
        prevPage.disabled = currentPage === 1;
        prevPage.onclick = () => {
            if (currentPage > 1) {
                currentPage--;
                filterAndDisplayContacts();
            }
        };
    }

    if (nextPage) {
        nextPage.disabled = currentPage === totalPages;
        nextPage.onclick = () => {
            if (currentPage < totalPages) {
                currentPage++;
                filterAndDisplayContacts();
            }
        };
    }
}

// Add event listeners to table buttons
function addTableButtonListeners() {
    document.querySelectorAll('.edit-contact').forEach(button => {
        button.addEventListener('click', (e) => {
            const row = e.target.closest('tr');
            const contact = currentContacts.find(c => c.Serial === parseInt(row.dataset.id));
            openEditModal(contact);
        });
    });

    document.querySelectorAll('.delete-contact').forEach(button => {
        button.addEventListener('click', (e) => {
            const row = e.target.closest('tr');
            if (confirm('Are you sure you want to delete this contact?')) {
                deleteContact(parseInt(row.dataset.id));
            }
        });
    });
}

// Add event listeners to checkboxes
function addCheckboxListeners() {
    // Individual row checkboxes
    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            const row = e.target.closest('tr');
            if (e.target.checked) {
                row.classList.add('row-selected');
            } else {
                row.classList.remove('row-selected');
            }
            updateButtonStates();
        });
    });
}

// Update button states based on selected rows
function updateButtonStates() {
    const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
    const selectedCount = selectedCheckboxes.length;

    // Enable/disable edit button (only for single selection)
    if (editButton) {
        editButton.disabled = selectedCount !== 1;
    }

    // Enable/disable delete button (for one or more selections)
    if (deleteButton) {
        deleteButton.disabled = selectedCount === 0;
    }
}

// Get selected contact data
function getSelectedContact() {
    const selectedCheckbox = document.querySelector('.row-checkbox:checked');
    if (selectedCheckbox) {
        const serialNumber = parseInt(selectedCheckbox.value);
        return currentContacts.find(c => c.Serial === serialNumber);
    }
    return null;
}

// Get all selected contact IDs
function getSelectedContactIds() {
    const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
    return Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));
}

// Open modal for adding/editing contact
function openEditModal(contact = null) {
    const crudModal = new bootstrap.Modal(document.getElementById('crudModal'));
    const saveContactButton = document.getElementById('saveContactButton');

    document.getElementById('crudModalLabel').textContent = contact ? 'Edit Contact' : 'Add Contact';
    document.getElementById('contactId').value = contact?.Serial || '';
    document.getElementById('contactName').value = contact?.ContactName || '';
    document.getElementById('firstName').value = contact?.FirstName || '';
    document.getElementById('contactNumber').value = contact?.Contactnumber || '';
    document.getElementById('contactWhatsApp').value = contact?.WhatsApp || 'No';
    document.getElementById('contactBirthday').value = contact?.Birthday || '';

    // Populate contact group dropdown for modal
    populateModalContactGroups(contact?.ContactGroup || '');

    // Set up the save button event handler
    const existingHandler = saveContactButton.onclick;
    if (existingHandler) {
        saveContactButton.removeEventListener('click', existingHandler);
    }

    saveContactButton.onclick = () => saveContact(contact?.Serial);

    crudModal.show();
}

// Populate contact groups for modal dropdown
function populateModalContactGroups(selectedGroup = '') {
    const contactGroupSelect = document.getElementById('contactGroup');

    // Get unique contact groups from current data
    const groups = [...new Set(currentContacts.map(contact => contact.ContactGroup).filter(Boolean))];

    // Clear existing options
    contactGroupSelect.innerHTML = '';

    // Add default option
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = 'Select a group...';
    contactGroupSelect.appendChild(defaultOption);

    // Add options for each group
    groups.forEach(group => {
        const option = document.createElement('option');
        option.value = group;
        option.textContent = group;
        if (group === selectedGroup) {
            option.selected = true;
        }
        contactGroupSelect.appendChild(option);
    });

    // Add common groups if they don't exist
    const commonGroups = ['Alpha', 'Beta', 'Gamma', 'Delta', 'Leaders', 'Admins'];
    commonGroups.forEach(group => {
        if (!groups.includes(group)) {
            const option = document.createElement('option');
            option.value = group;
            option.textContent = group;
            if (group === selectedGroup) {
                option.selected = true;
            }
            contactGroupSelect.appendChild(option);
        }
    });
}

// Save contact (create or update) using B4J REST API
async function saveContact(serialNumber = null) {
    const contactData = {
        FirstName: document.getElementById('firstName').value,
        Contactnumber: document.getElementById('contactNumber').value,
        ContactGroup: document.getElementById('contactGroup').value,
        WhatsApp: document.getElementById('contactWhatsApp').value,
        Birthday: document.getElementById('contactBirthday').value,
        ContactName: document.getElementById('contactName').value || `${document.getElementById('contactGroup').value} - ${document.getElementById('firstName').value}`,
        Active: "0",
        ContactPhoneType: "mobile",
        ContacteMail: "",
        ContacteMailType: "",
        ContactImage: "(Bitmap) Not initialized",
        Age: null,
        Notes: "",
        Groep: null
    };

    try {
        console.log(`Saving contact data to B4J API...`);

        let apiUrl, method;

        if (serialNumber) {
            // Update existing contact
            apiUrl = `${API_BASE_URL}/contacts/${serialNumber}`;
            method = 'PUT';
        } else {
            // Create new contact
            apiUrl = `${API_BASE_URL}/contacts`;
            method = 'POST';
        }

        console.log(`${method} request to:`, apiUrl);
        console.log('Contact data:', contactData);

        const response = await fetch(apiUrl, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(contactData)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
        }

        const result = await response.json();
        console.log('Save response from B4J API:', result);

        bootstrap.Modal.getInstance(document.getElementById('crudModal')).hide();
        await fetchContacts(); // Refresh the table
        alert(`Contact ${serialNumber ? 'updated' : 'added'} successfully!`);
    } catch (error) {
        console.error('Error saving contact to B4J API:', error);
        alert('Error saving contact to database: ' + error.message);
    }
}

// Delete contact using B4J REST API
async function deleteContact(serialNumber) {
    try {
        console.log(`Deleting contact with serial number: ${serialNumber}`);

        const apiUrl = `${API_BASE_URL}/contacts/${serialNumber}`;
        console.log('DELETE request to:', apiUrl);

        const response = await fetch(apiUrl, {
            method: 'DELETE',
            headers: {
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
        }

        const result = await response.json();
        console.log('Delete response from B4J API:', result);

        await fetchContacts(); // Refresh the table
        alert('Contact deleted successfully!');
    } catch (error) {
        console.error('Error deleting contact from B4J API:', error);
        alert('Error deleting contact from database: ' + error.message);
    }
}

// Delete multiple contacts
async function deleteMultipleContacts(serialNumbers) {
    try {
        console.log(`Deleting contacts with serial numbers: ${serialNumbers.join(', ')}`);

        // Delete contacts one by one (you could also implement batch delete in B4J)
        for (const serialNumber of serialNumbers) {
            const apiUrl = `${API_BASE_URL}/contacts/${serialNumber}`;
            console.log('DELETE request to:', apiUrl);

            const response = await fetch(apiUrl, {
                method: 'DELETE',
                headers: {
                    'Accept': 'application/json'
                }
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
            }
        }

        await fetchContacts(); // Refresh the table
        alert(`${serialNumbers.length} contact(s) deleted successfully!`);
    } catch (error) {
        console.error('Error deleting contacts from B4J API:', error);
        alert('Error deleting contacts from database: ' + error.message);
    }
}

// Add a variable to track the current data source
let currentDataSource = 'contacts'; // Can be 'contacts' or 'brothers'

// Event Listeners
if (loginForm) {
    loginForm.addEventListener('submit', login);
}

if (logoutButton) {
    logoutButton.addEventListener('click', (e) => {
        e.preventDefault();
        logout();
    });
}

// Add event listener for List All Brothers link
document.getElementById('listAllBrothersLink')?.addEventListener('click', (e) => {
    e.preventDefault();
    fetchBrothers();
});

searchButton?.addEventListener('click', filterAndDisplayContacts);
searchInput?.addEventListener('keyup', (e) => {
    if (e.key === 'Enter') filterAndDisplayContacts();
});
groupFilter?.addEventListener('change', filterAndDisplayContacts);
whatsappFilter?.addEventListener('change', filterAndDisplayContacts);
birthdayFilter?.addEventListener('change', filterAndDisplayContacts);
itemsPerPageSelect?.addEventListener('change', () => {
    currentPage = 1; // Reset to first page when changing items per page
    filterAndDisplayContacts();
});

// Select All checkbox functionality
selectAllCheckbox?.addEventListener('change', (e) => {
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    rowCheckboxes.forEach(checkbox => {
        checkbox.checked = e.target.checked;
        const row = checkbox.closest('tr');
        if (e.target.checked) {
            row.classList.add('row-selected');
        } else {
            row.classList.remove('row-selected');
        }
    });
    updateButtonStates();
});

// CRUD button event listeners
addButton?.addEventListener('click', () => openEditModal());

editButton?.addEventListener('click', () => {
    const selectedContact = getSelectedContact();
    if (selectedContact) {
        openEditModal(selectedContact);
    }
});

deleteButton?.addEventListener('click', () => {
    const selectedIds = getSelectedContactIds();
    if (selectedIds.length > 0) {
        const message = selectedIds.length === 1
            ? 'Are you sure you want to delete this contact?'
            : `Are you sure you want to delete ${selectedIds.length} contacts?`;

        if (confirm(message)) {
            deleteMultipleContacts(selectedIds);
        }
    }
});

// Navigation event listeners
document.querySelector('a[href="#dashboard"]')?.addEventListener('click', (e) => {
    e.preventDefault();
    loadDashboard();
});

listAllBrothersLink?.addEventListener('click', (e) => {
    e.preventDefault();
    showBrothersList();
});

// Logout functionality
logoutButton?.addEventListener('click', (e) => {
    e.preventDefault();
    logout();
});

// Initial load
document.addEventListener('DOMContentLoaded', () => {
    // Set current date in the top nav bar
    const currentDateElement = document.getElementById('currentDate');
    if (currentDateElement) {
        const today = new Date();
        const options = { weekday: 'long', year: 'numeric', month: 'short', day: 'numeric' };
        currentDateElement.textContent = today.toLocaleDateString('en-US', options);
    }

    // Show live system message in message area
    const messageArea = document.getElementById('messageArea');
    if (messageArea) {
        messageArea.textContent = 'Live System - Connected to B4J REST API Server (Login Bypassed)';
    }

    console.log('Running in LIVE MODE with B4J REST API Server - Login Bypassed for Testing');

    // Bypass login and show main application with dashboard
    showMainApplication();

    // Load dashboard by default
    loadDashboard();
});