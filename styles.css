body {
    background-color: #000 !important; /* Solid black background */
    font-family: 'Raleway', sans-serif;
    padding-top: 55px; /* Space for top navbar */
    padding-bottom: 55px; /* Space for footer */
    margin: 0;
    min-height: 100vh;
    color: #fff;
}

.table {
    background-color: #1a1a1a;
    font-size: 12px;
}

h1 {
    margin-left: 100px;
}

table tr td {
    padding-top: calc(0.75rem - 6px); /* Adjust the top padding */
    padding-bottom: calc(0.75rem - 6px); /* Adjust the bottom padding */
}

.form-control, .form-select {
    border-color: #444;
    background-color: #343a40;
    color: #fff;
    border-color: #495057;
}

.form-control:focus, .form-select:focus {
    background-color: #343a40 !important;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    color: #fff !important;
}

.form-control::placeholder {
    color: #6c757d;
    opacity: 1;
}

.modal-content {
    background-color: #222 !important;
    border: 1px solid #444;
}

.modal-content.bg-dark {
    border: 1px solid #495057;
}

.modal-header {
    border-bottom: 1px solid #495057;
}

.modal-footer {
    border-top: 1px solid #495057;
}

.table-dark {
    --bs-table-bg: #1a1a1a;
    --bs-table-striped-bg: #222;
    --bs-table-hover-bg: #2a2a2a;
}

.btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}

.bi {
    font-size: 1.1em;
    margin-right: 0.3em;
}

.position-fixed {
    z-index: 1030;
}

.position-fixed.bottom-0.end-0 {
    right: 20px !important;
    bottom: 20px !important;
    z-index: 1030;
}

/* Custom scrollbar for better visibility */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #444;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
/* Ensure the background colors are applied properly */
.bg-success, .bg-danger {
    color: white; /* Ensure text is readable */
    padding: 0.5rem; /* Add some padding for better appearance */
}

/* Override Bootstrap's default table hover effect for the WhatsApp column */
.table-dark .bg-success:hover, .table-dark .bg-danger:hover {
    background-color: inherit !important; /* Prevent hover effect from changing the color */
}

.whatsapp-column {
    background-color: transparent; /* Remove background color */
    font-weight: bold; /* Make text bold */
    color: #0074cc; /* Change text color to a desired color (e.g., blue) */
}

/* Login Screen */
#loginScreen {
    background-color: #000 !important;
    padding-top: 55px; /* Account for top navbar */
    padding-bottom: 55px; /* Account for footer */
    height: calc(100vh - 110px) !important;
}

#loginScreen .card {
    background-color: #000000 !important;
    border: 1px solid #dc3545 !important;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#loginScreen input::placeholder {
    color: #6c757d;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    position: fixed;
    top: 0; /* Start from very top */
    left: 0;
    height: 100vh; /* Full viewport height */
    overflow-y: auto;
    z-index: 1000;
    padding-top: 1rem;
    transition: all 0.3s;
    background-color: #000000 !important;
    border-right: 1px solid #dc3545 !important;
}

.sidebar hr {
    margin: 1rem 0.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.sidebar .nav-link {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    color: #e9ecef !important;
    transition: all 0.2s;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link .bi {
    font-size: 1.1rem;
}

.sidebar .collapse .nav-link {
    padding-left: 2.5rem;
    font-size: 0.85rem;
}

/* Main Content Area */
.main-content {
    margin-left: 250px;
    padding: 1rem;
    width: calc(100% - 250px);
    min-height: calc(100vh - 110px); /* Adjust for navbar and footer */
    background-color: #000000 !important;
    border: 1px solid #dc3545 !important;
    border-left: none; /* Remove left border since sidebar has right border */
}

/* Main Application Layout */
#mainApplication {
    display: flex;
    min-height: 100vh;
}

/* Loading Spinner */
.loading-spinner {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1050;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .position-fixed.bottom-0.end-0 {
        position: relative !important;
        bottom: auto !important;
        right: auto !important;
        display: flex;
        justify-content: center;
        margin-top: 1rem;
    }
}

/* Top Message Bar */
.top-nav-bar {
    position: fixed;
    top: 0;
    left: 250px; /* Start from right edge of sidebar */
    width: calc(100% - 250px); /* Full width minus sidebar */
    height: 55px;
    background-color: #000000 !important;
    border-bottom: 1px solid #dc3545 !important;
    color: #fff;
    z-index: 1050;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.top-nav-bar .dev-mode-indicator {
    font-weight: bold;
    background-color: #ffc107;
    color: #212529;
    padding: 4px 10px;
    border-radius: 4px;
    margin-right: 10px;
}

.top-nav-bar .page-title {
    flex-grow: 1;
    text-align: left;
    font-weight: 600;
    font-size: 1.2rem;
    margin-left: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Footer Bar */
.footer-bar {
    position: fixed;
    bottom: 0;
    left: 250px; /* Start from right edge of sidebar */
    width: calc(100% - 250px); /* Full width minus sidebar */
    height: 55px;
    background-color: #000000 !important;
    border-top: 1px solid #dc3545 !important;
    color: #fff;
    z-index: 1030;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.2);
}

.footer-bar .copyright {
    font-size: 0.9rem;
}

.footer-bar .message-area {
    flex-grow: 1;
    text-align: center;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.footer-bar .version {
    font-size: 0.8rem;
    color: #adb5bd;
}
