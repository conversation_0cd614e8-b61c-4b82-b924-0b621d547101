# B4J Server Endpoints for Data Cleanse System

## Overview
This document outlines the B4J server endpoints needed to handle the data cleanse form submissions and WhatsApp confirmations.

## Required Endpoints

### 1. Form Display Endpoint
**URL:** `/manne/form`  
**Method:** GET  
**Purpose:** Serve the data cleanse form with pre-filled data

**Parameters (URL Query):**
- `serial` - Contact serial number
- `name` - Full contact name
- `firstname` - First name
- `email` - Email address
- `phone` - Phone number
- `birthday` - Birthday (DD/MM/YYYY format)
- `group` - Current group
- `whatsapp` - WhatsApp status

**Response:** HTML form (data-cleanse-form.html)

### 2. Data Update Endpoint
**URL:** `/manne/update`  
**Method:** POST  
**Content-Type:** application/json  
**Purpose:** Process form submission and update database

**Request Body:**
```json
{
    "serial": "16",
    "firstName": "Bill",
    "lastName": "<PERSON>",
    "phone": "27820511229",
    "email": "<EMAIL>",
    "birthday": "1960-02-20",
    "group": "Green Olive"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Data updated successfully",
    "messageId": "whatsapp_confirmation_id"
}
```

**Actions to Perform:**
1. Update database with new information
2. Send WhatsApp confirmation: "Baie dankie! Jou besonderhede is opgedateer."
3. Return success response

### 3. Moved Contact Endpoint
**URL:** `/manne/moved`  
**Method:** POST  
**Content-Type:** application/json  
**Purpose:** Mark contact as moved/inactive

**Request Body:**
```json
{
    "serial": "16",
    "status": "moved"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Contact marked as moved"
}
```

**Actions to Perform:**
1. Update contact status to "moved" or "inactive"
2. Send WhatsApp confirmation: "Baie dankie vir jou tyd by die Brotherhood. Sterkte met jou nuwe avontuur!"
3. Return success response

## Database Updates Required

### Contact Table Updates
When processing form submissions, update these fields:
- `ContactName` - Combine firstName + lastName
- `FirstName` - From firstName field
- `Contactnumber` - From phone field
- `Email` - From email field
- `Birthday` - From birthday field (convert to your date format)
- `ContactGroup` - From group field
- `LastUpdated` - Current timestamp
- `UpdateSource` - "Data Cleanse Form"

### For Moved Contacts
- `Status` - Set to "Moved" or "Inactive"
- `WhatsApp` - Set to "No"
- `LastUpdated` - Current timestamp
- `Notes` - "Marked as moved via data cleanse form"

## WhatsApp Confirmation Messages

### Successful Update
```
Baie dankie! 

Jou besonderhede is suksesvol opgedateer in ons sisteem.

5:55 Administrasie
```

### Moved Contact
```
Baie dankie vir jou tyd by die Brotherhood. 

Sterkte met jou nuwe avontuur!

Jy is van ons lys afgehaal soos versoek.

5:55 Administrasie
```

## Error Handling

### Form Validation Errors
Return HTTP 400 with:
```json
{
    "success": false,
    "message": "Validation error: [specific error]",
    "errors": {
        "field": "error message"
    }
}
```

### Database Errors
Return HTTP 500 with:
```json
{
    "success": false,
    "message": "Database error occurred"
}
```

### WhatsApp Send Errors
- Still update database
- Log WhatsApp error
- Return success for form submission
- Retry WhatsApp later if possible

## Security Considerations

1. **Validate Serial Numbers** - Ensure serial exists in database
2. **Rate Limiting** - Prevent spam submissions
3. **Input Sanitization** - Clean all form inputs
4. **CORS Headers** - Allow form access from WhatsApp browsers
5. **HTTPS Only** - Secure data transmission

## Implementation Notes

### B4J Handler Structure
```vb
Sub Handle(req As ServletRequest, resp As ServletResponse)
    StartMessageLoop ' IMPORTANT: Required for async operations
    
    Select req.RequestURI
        Case "/manne/form"
            HandleFormDisplay(req, resp)
        Case "/manne/update"
            HandleDataUpdate(req, resp)
        Case "/manne/moved"
            HandleMovedContact(req, resp)
    End Select
End Sub
```

### CORS Headers (Important for WhatsApp)
```vb
resp.SetHeader("Access-Control-Allow-Origin", "*")
resp.SetHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
resp.SetHeader("Access-Control-Allow-Headers", "Content-Type")
```

### Green API Integration
Use your existing Green API credentials:
- Instance: 7103251786
- API Token: 047c85965206424dabd1b06fcda50307ab6f1fd73cc94cceb3
- Send confirmation messages after successful updates

## Testing Checklist

- [ ] Form displays correctly with pre-filled data
- [ ] Form validation works properly
- [ ] Database updates successfully
- [ ] WhatsApp confirmations are sent
- [ ] Error handling works correctly
- [ ] Mobile responsiveness verified
- [ ] CORS headers allow WhatsApp access
- [ ] "Moved" functionality works
- [ ] Success animations display properly

## File Deployment

1. Place `data-cleanse-form.html` in your B4J server's static files directory
2. Implement the three endpoints in your B4J handler
3. Test with the form URL from WhatsApp messages
4. Verify database updates and WhatsApp confirmations

This system will provide a seamless experience for Brothers to update their data via WhatsApp!
