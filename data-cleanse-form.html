<!DOCTYPE html>
<html lang="af">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>5:55 Manne - Data Update</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --brotherhood-red: #dc3545;
            --brotherhood-dark: #1a1a1a;
            --brotherhood-gold: #ffc107;
        }

        body {
            background: #000000;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
        }

        .brotherhood-header {
            background: #000000;
            color: white;
            padding: 2rem 0;
            text-align: center;
            border-bottom: 2px solid var(--brotherhood-red);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.8);
        }

        .logo-container {
            text-align: center;
        }

        .brotherhood-logo-img {
            max-height: 80px;
            max-width: 200px;
            height: auto;
            width: auto;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
            border-radius: 10px;
        }

        .brotherhood-logo {
            font-size: 3rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 0.5rem;
        }

        .brotherhood-subtitle {
            font-size: 1.2rem;
            font-style: italic;
            opacity: 0.9;
        }

        .form-container {
            background: #000000;
            border-radius: 20px;
            border: 2px solid var(--brotherhood-red);
            box-shadow: 0 8px 32px rgba(220, 53, 69, 0.3);
            padding: 2rem;
            margin: 2rem auto;
            max-width: 600px;
        }

        .form-control, .form-select {
            background: #000000;
            border: 1px solid var(--brotherhood-red);
            color: white;
            border-radius: 10px;
            padding: 0.75rem;
        }

        .form-control:focus, .form-select:focus {
            background: #000000;
            border-color: var(--brotherhood-gold);
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
            color: white;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        /* Fix dropdown option colors */
        .form-select option {
            background-color: #2c2c2c;
            color: white;
            padding: 0.5rem;
        }

        .form-select option:hover {
            background-color: var(--brotherhood-red);
            color: white;
        }

        .form-select option:checked {
            background-color: var(--brotherhood-red);
            color: white;
        }

        /* Enhanced browser compatibility for dropdowns */
        select.form-select {
            background-color: #000000 !important;
            color: white !important;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        select.form-select option {
            background-color: #000000 !important;
            color: white !important;
            padding: 8px 12px;
        }

        select.form-select option:hover,
        select.form-select option:focus,
        select.form-select option:active,
        select.form-select option:checked {
            background-color: var(--brotherhood-red) !important;
            color: white !important;
        }

        /* Webkit specific fixes for Safari/Chrome */
        select.form-select::-webkit-scrollbar {
            width: 8px;
        }

        select.form-select::-webkit-scrollbar-track {
            background: #2c2c2c;
        }

        select.form-select::-webkit-scrollbar-thumb {
            background: var(--brotherhood-red);
            border-radius: 4px;
        }

        /* Firefox specific fixes */
        @-moz-document url-prefix() {
            select.form-select {
                background-color: #000000 !important;
                color: white !important;
            }

            select.form-select option {
                background-color: #000000 !important;
                color: white !important;
            }
        }

        .form-label {
            color: var(--brotherhood-gold);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .btn-brotherhood {
            background: linear-gradient(45deg, var(--brotherhood-red), #ff4757);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
        }

        .btn-brotherhood:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220, 53, 69, 0.6);
            color: white;
        }

        .btn-moved {
            background: linear-gradient(45deg, #6c757d, #495057);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-moved:hover {
            transform: translateY(-2px);
            color: white;
        }

        .current-data {
            background: #000000;
            border: 2px solid var(--brotherhood-gold);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .current-data h5 {
            color: var(--brotherhood-gold);
            margin-bottom: 1rem;
        }

        .data-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .data-item:last-child {
            border-bottom: none;
        }

        .data-label {
            font-weight: 600;
            color: var(--brotherhood-gold);
        }

        .data-value {
            color: white;
            text-align: right;
        }

        .success-animation {
            display: none;
            text-align: center;
            padding: 3rem;
        }

        .success-icon {
            font-size: 4rem;
            color: #28a745;
            animation: bounce 1s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .footer-signature {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: var(--brotherhood-gold);
            font-style: italic;
        }

        @media (max-width: 768px) {
            .brotherhood-logo-img {
                max-height: 60px;
                max-width: 150px;
            }

            .brotherhood-logo {
                font-size: 2rem;
            }

            .form-container {
                margin: 1rem;
                padding: 1.5rem;
            }

            .data-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .data-value {
                text-align: left;
                margin-top: 0.25rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="brotherhood-header">
        <div class="container">
            <div class="logo-container mb-3">
                <img src="555 logo.png" alt="555 Logo" class="brotherhood-logo-img">
            </div>
            <div class="brotherhood-logo">🔴⚪ 5:55 MANNE ⚪🔴</div>
            <div class="brotherhood-subtitle">Your Story - HIS Glory</div>
        </div>
    </div>

    <!-- Main Form Container -->
    <div class="container">
        <div class="form-container">
            <!-- Current Data Display -->
            <div class="current-data" id="currentDataSection">
                <h5><i class="bi bi-person-circle me-2"></i>Jou Huidige Besonderhede</h5>
                <div class="data-item">
                    <span class="data-label"><i class="bi bi-person me-2"></i>Naam:</span>
                    <span class="data-value" id="currentName">-</span>
                </div>
                <div class="data-item">
                    <span class="data-label"><i class="bi bi-telephone me-2"></i>Sel:</span>
                    <span class="data-value" id="currentPhone">-</span>
                </div>
                <div class="data-item">
                    <span class="data-label"><i class="bi bi-envelope me-2"></i>Epos:</span>
                    <span class="data-value" id="currentEmail">-</span>
                </div>
                <div class="data-item">
                    <span class="data-label"><i class="bi bi-calendar me-2"></i>Geboorte:</span>
                    <span class="data-value" id="currentBirthday">-</span>
                </div>
                <div class="data-item">
                    <span class="data-label"><i class="bi bi-people me-2"></i>Groep:</span>
                    <span class="data-value" id="currentGroup">-</span>
                </div>
                <div class="data-item">
                    <span class="data-label"><i class="bi bi-whatsapp me-2"></i>WhatsApp:</span>
                    <span class="data-value" id="currentWhatsapp">-</span>
                </div>
            </div>

            <!-- Update Form -->
            <form id="dataUpdateForm">
                <input type="hidden" id="serial" name="serial">

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="firstName" class="form-label">
                            <i class="bi bi-person me-2"></i>Voornaam
                        </label>
                        <input type="text" class="form-control" id="firstName" name="firstName" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="lastName" class="form-label">
                            <i class="bi bi-person me-2"></i>Van
                        </label>
                        <input type="text" class="form-control" id="lastName" name="lastName" required>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="phone" class="form-label">
                        <i class="bi bi-telephone me-2"></i>Sel Nommer
                    </label>
                    <input type="tel" class="form-control" id="phone" name="phone" required>
                </div>

                <div class="mb-3">
                    <label for="email" class="form-label">
                        <i class="bi bi-envelope me-2"></i>Epos Adres
                    </label>
                    <input type="email" class="form-control" id="email" name="email">
                </div>

                <div class="mb-3">
                    <label for="birthday" class="form-label">
                        <i class="bi bi-calendar me-2"></i>Geboorte Datum
                    </label>
                    <input type="date" class="form-control" id="birthday" name="birthday">
                </div>

                <div class="mb-3">
                    <label for="group" class="form-label">
                        <i class="bi bi-people me-2"></i>Groep Keuse
                    </label>
                    <select class="form-select" id="group" name="group" required onchange="toggleSecondGroup()">
                        <option value="">Kies jou groep...</option>
                        <option value="Green Olive">Green Olive</option>
                        <option value="2'Morrows">2'Morrows</option>
                        <option value="Zuiderster">Zuiderster</option>
                        <option value="Red Truck">Red Truck</option>
                        <option value="Florauna Bakkery">Florauna Bakkery</option>
                        <option value="Aan Beweeg">Aan Beweeg (Nie meer gekontak wil wees nie)</option>
                    </select>
                </div>

                <!-- Second Group Selection (Hidden by default) -->
                <div class="mb-4" id="secondGroupContainer" style="display: none;">
                    <label for="secondGroup" class="form-label">
                        <i class="bi bi-people me-2"></i>My Tweede Groep Keuse
                    </label>
                    <select class="form-select" id="secondGroup" name="secondGroup">
                        <option value="">Kies jou tweede groep...</option>
                        <option value="Geen">Geen</option>
                        <!-- Options will be populated dynamically -->
                    </select>
                </div>

                <!-- Action Buttons -->
                <div class="d-grid gap-3">
                    <button type="submit" class="btn btn-brotherhood">
                        <i class="bi bi-check-circle me-2"></i>Stuur Opdatering
                    </button>
                    <button type="button" class="btn btn-moved" onclick="markAsMoved()">
                        <i class="bi bi-arrow-right-circle me-2"></i>Ek het Aanbeweeg
                    </button>
                </div>
            </form>

            <!-- Success Animation (Hidden) -->
            <div class="success-animation" id="successAnimation">
                <div class="success-icon">
                    <i class="bi bi-check-circle-fill"></i>
                </div>
                <h3 class="mt-3">Baie Dankie!</h3>
                <p>Jou besonderhede is suksesvol opgedateer.</p>
                <p><small>Jy sal 'n bevestiging op WhatsApp ontvang.</small></p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer-signature">
            <strong>5:55 Administrasie</strong><br>
            <small>Brotherhood of 555 - Your Story, HIS Glory</small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Parse URL parameters and populate form
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);

            // Populate current data display
            document.getElementById('currentName').textContent = urlParams.get('name') || 'Nie beskikbaar';
            document.getElementById('currentPhone').textContent = urlParams.get('phone') || 'Nie beskikbaar';
            document.getElementById('currentEmail').textContent = urlParams.get('email') || 'Nie beskikbaar';
            document.getElementById('currentBirthday').textContent = urlParams.get('birthday') || 'Geen rekord nie';
            document.getElementById('currentGroup').textContent = urlParams.get('group') || 'Nie beskikbaar';
            document.getElementById('currentWhatsapp').textContent = urlParams.get('whatsapp') || 'No';

            // Populate form fields
            document.getElementById('serial').value = urlParams.get('serial') || '';

            // Split name into first and last name
            const fullName = urlParams.get('name') || '';
            const nameParts = fullName.split(' ');
            document.getElementById('firstName').value = urlParams.get('firstname') || nameParts[0] || '';
            document.getElementById('lastName').value = nameParts.slice(1).join(' ') || '';

            document.getElementById('phone').value = urlParams.get('phone') || '';
            document.getElementById('email').value = urlParams.get('email') || '';
            document.getElementById('birthday').value = urlParams.get('birthday') || '';
            document.getElementById('group').value = urlParams.get('group') || '';
        });

        // Handle form submission
        document.getElementById('dataUpdateForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Besig om te stuur...';
            submitBtn.disabled = true;

            // Add second group to data if selected
            const secondGroup = document.getElementById('secondGroup').value;
            if (secondGroup && secondGroup !== '') {
                data.secondGroup = secondGroup;
            }

            // Submit to B4J server
            fetch('/manne/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showSuccess();
                } else {
                    throw new Error(result.message || 'Update failed');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Fout: ' + error.message);
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Handle "Moved" button
        function markAsMoved() {
            if (confirm('Is jy seker jy wil as "Aanbeweeg" gemerk word? Ons sal jou van ons lys afhaal.')) {
                const serial = document.getElementById('serial').value;

                fetch('/manne/moved', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ serial: serial, status: 'moved' })
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        showSuccess('Jy is suksesvol as "Aanbeweeg" gemerk. Baie dankie vir jou tyd by die Brotherhood.');
                    } else {
                        throw new Error(result.message || 'Update failed');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Fout: ' + error.message);
                });
            }
        }

        // Show success animation
        function showSuccess(customMessage = null) {
            document.getElementById('currentDataSection').style.display = 'none';
            document.getElementById('dataUpdateForm').style.display = 'none';

            const successDiv = document.getElementById('successAnimation');
            if (customMessage) {
                successDiv.querySelector('p').textContent = customMessage;
            }
            successDiv.style.display = 'block';
        }

        // Toggle second group selection based on first group choice
        function toggleSecondGroup() {
            const firstGroup = document.getElementById('group');
            const secondGroupContainer = document.getElementById('secondGroupContainer');
            const secondGroup = document.getElementById('secondGroup');

            if (firstGroup.value && firstGroup.value !== '' && firstGroup.value !== 'Aan Beweeg') {
                // Show second group selection
                secondGroupContainer.style.display = 'block';

                // Clear and populate second group options (excluding the selected first group)
                secondGroup.innerHTML = '<option value="">Kies jou tweede groep...</option><option value="Geen">Geen</option>';

                const allGroups = [
                    'Green Olive',
                    "2'Morrows",
                    'Zuiderster',
                    'Red Truck',
                    'Florauna Bakkery'
                ];

                // Add all groups except the selected one
                allGroups.forEach(function(group) {
                    if (group !== firstGroup.value) {
                        const option = document.createElement('option');
                        option.value = group;
                        option.textContent = group;
                        secondGroup.appendChild(option);
                    }
                });

            } else {
                // Hide second group selection
                secondGroupContainer.style.display = 'none';
                secondGroup.value = '';
            }
        }
    </script>
</body>
</html>
