<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Raleway:wght@400&display=swap" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <link rel="icon" href="data:image/x-icon;," type="image/x-icon">
    <script src="https://cdn.jsdelivr.net/npm/fingerprintjs2@2.1.4/dist/fingerprint2.min.js"></script>
    <style>
        body { font-family: 'Raleway', sans-serif; }
        .table {
            font-size: 11px;
            --bs-table-cell-padding-y: 0.25rem;
            --bs-table-cell-padding-x: 0.5rem;
        }
        .table td, .table th {
            vertical-align: middle;
            padding: 0.25rem 0.5rem;
        }
        .btn-xs {
            padding: 0.125rem 0.25rem;
            font-size: 0.75rem;
            line-height: 1.2;
        }
        .loading-spinner {
            display: none; /* Hidden by default */
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1050;
        }
        .row-selected {
            background-color: rgba(13, 110, 253, 0.25) !important;
        }
        .crud-btn {
            min-width: 80px;
            height: 38px; /* Fixed height for all buttons */
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .crud-btn#addButton {
            background-color: #198754 !important;
            border-color: #198754 !important;
            color: #ffffff !important;
        }
        .crud-btn#addButton:hover {
            background-color: #157347 !important;
            border-color: #146c43 !important;
        }
        .crud-btn#editButton {
            background-color: #0d6efd !important;
            border-color: #0d6efd !important;
            color: #ffffff !important;
        }
        .crud-btn#editButton:hover {
            background-color: #0b5ed7 !important;
            border-color: #0a58ca !important;
        }
        .crud-btn#deleteButton {
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
            color: #ffffff !important;
        }
        .crud-btn#deleteButton:hover {
            background-color: #bb2d3b !important;
            border-color: #b02a37 !important;
        }
        .crud-btn:disabled {
            background-color: #6c757d !important;
            border-color: #6c757d !important;
            color: #ffffff !important;
            opacity: 0.65;
        }
        .whatsapp-cell {
            font-weight: bold !important;
            text-align: center !important;
            color: #000000 !important;
        }
        .whatsapp-yes {
            background-color: #32CD32 !important;
            color: #000000 !important;
        }
        .whatsapp-no {
            background-color: #FF0000 !important;
            color: #000000 !important;
        }
        /* NUCLEAR OPTION - FORCE WhatsApp colors with MAXIMUM !important */
        .whatsapp-yes {
            background: #32CD32 !important;
            background-color: #32CD32 !important;
            color: #000000 !important;
            font-weight: bold !important;
            text-align: center !important;
        }

        .whatsapp-no {
            background: #FF0000 !important;
            background-color: #FF0000 !important;
            color: #000000 !important;
            font-weight: bold !important;
            text-align: center !important;
        }

        /* Override EVERYTHING with maximum specificity and !important */
        * td.whatsapp-yes,
        * tr td.whatsapp-yes,
        * tbody tr td.whatsapp-yes,
        * table tbody tr td.whatsapp-yes,
        * .table tbody tr td.whatsapp-yes,
        * .table-dark tbody tr td.whatsapp-yes,
        * tr:hover td.whatsapp-yes,
        * tr.row-selected td.whatsapp-yes,
        * .row-selected td.whatsapp-yes,
        * .table-dark tr:hover td.whatsapp-yes,
        * .table-dark tr.row-selected td.whatsapp-yes,
        * .table-striped tbody tr:nth-of-type(odd) td.whatsapp-yes,
        * .table-striped tbody tr:nth-of-type(even) td.whatsapp-yes {
            background: #32CD32 !important;
            background-color: #32CD32 !important;
            color: #000000 !important;
            font-weight: bold !important;
            text-align: center !important;
        }

        * td.whatsapp-no,
        * tr td.whatsapp-no,
        * tbody tr td.whatsapp-no,
        * table tbody tr td.whatsapp-no,
        * .table tbody tr td.whatsapp-no,
        * .table-dark tbody tr td.whatsapp-no,
        * tr:hover td.whatsapp-no,
        * tr.row-selected td.whatsapp-no,
        * .row-selected td.whatsapp-no,
        * .table-dark tr:hover td.whatsapp-no,
        * .table-dark tr.row-selected td.whatsapp-no,
        * .table-striped tbody tr:nth-of-type(odd) td.whatsapp-no,
        * .table-striped tbody tr:nth-of-type(even) td.whatsapp-no {
            background: #FF0000 !important;
            background-color: #FF0000 !important;
            color: #000000 !important;
            font-weight: bold !important;
            text-align: center !important;
        }
    </style>
</head>
<body class="bg-dark text-light">
    <!-- Top Navigation Bar -->
    <div class="top-nav-bar">
        <div class="d-flex align-items-center">
            <span id="devModeIndicator" class="dev-mode-indicator d-none">DEV MODE</span>
        </div>
        <div class="page-title" id="pageTitle">Dashboard</div>
        <div class="d-flex align-items-center">
            <span id="currentDate"></span>
        </div>
    </div>

    <!-- Login Screen - shown before authentication -->
    <div id="loginScreen" class="container-fluid vh-100 d-flex justify-content-center align-items-center">
        <div class="card bg-dark text-light" style="width: 400px;">
            <div class="card-header text-center">
                <img src="555 logo.png" width="175px" alt="555 Logo" class="my-3">
                <h3>Login</h3>
            </div>
            <div class="card-body">
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control bg-dark text-light" id="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control bg-dark text-light" id="password" required>
                    </div>
                    <div id="loginMessage" class="alert alert-danger d-none"></div>
                    <button type="submit" class="btn btn-primary w-100">Login</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Main Application - hidden until authenticated -->
    <div id="mainApplication" class="d-none">
        <!-- Sidebar -->
        <div class="sidebar bg-secondary">
            <div class="text-center p-3">
                <img src="555 logo.png" width="175px" alt="Logo">
            </div>
            <hr class="text-light">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link text-light" href="#dashboard"><i class="bi bi-speedometer2 me-2" style="color: #0d6efd;"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-light collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#brothersSubmenu" aria-expanded="false"><i class="bi bi-people-fill me-2" style="color: #198754;"></i>Brothers</a>
                    <ul class="collapse list-unstyled" id="brothersSubmenu">
                        <li><a class="nav-link text-light ms-4" href="#list-all" id="listAllBrothersLink"><i class="bi bi-list-ul me-2" style="color: #20c997;"></i>List All</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-light collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#commSubmenu" aria-expanded="false"><i class="bi bi-chat-dots-fill me-2" style="color: #fd7e14;"></i>Communication</a>
                    <ul class="collapse list-unstyled" id="commSubmenu">
                        <li><a class="nav-link text-light ms-4" href="#quick-message"><i class="bi bi-lightning-fill me-2" style="color: #ffc107;"></i>Quick Message</a></li>
                        <li><a class="nav-link text-light ms-4" href="#campaigns"><i class="bi bi-megaphone-fill me-2" style="color: #ffc107;"></i>Campaigns</a></li>
                        <li><a class="nav-link text-light ms-4" href="#social-media"><i class="bi bi-share-fill me-2" style="color: #ffc107;"></i>Social Media</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-light collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#adminSubmenu" aria-expanded="false"><i class="bi bi-person-badge-fill me-2" style="color: #6f42c1;"></i>Leaders and Admins</a>
                    <ul class="collapse list-unstyled" id="adminSubmenu">
                        <li><a class="nav-link text-light ms-4" href="#admin-list"><i class="bi bi-person-lines-fill me-2" style="color: #d63384;"></i>List</a></li>
                        <li><a class="nav-link text-light ms-4" href="#admin-update"><i class="bi bi-person-gear me-2" style="color: #d63384;"></i>Update</a></li>
                    </ul>
                </li>
                <li class="nav-item mt-auto">
                    <a class="nav-link text-light" href="#" id="logoutButton"><i class="bi bi-box-arrow-right me-2" style="color: #dc3545;"></i>Logout</a>
                </li>
            </ul>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <div class="container-fluid py-4">
                <!-- Dashboard Section -->
                <div id="dashboardSection">
                    <h1 class="mb-4">Dashboard</h1>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="card-title" id="totalContactsCount">0</h4>
                                            <p class="card-text">Total Contacts</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-people-fill" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="card-title" id="whatsappYesCount">0</h4>
                                            <p class="card-text">WhatsApp Enabled</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-whatsapp" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="card-title" id="totalGroupsCount">0</h4>
                                            <p class="card-text">Contact Groups</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-collection-fill" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="card-title" id="birthdaysThisMonthCount">0</h4>
                                            <p class="card-text">Birthdays This Month</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-calendar-event-fill" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-dark border-secondary">
                                <div class="card-header">
                                    <h5 class="mb-0">Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <button class="btn btn-primary w-100 mb-2" onclick="showBrothersList()">
                                                <i class="bi bi-people-fill me-2"></i>View Brothers List
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-success w-100 mb-2" onclick="addNewContact()">
                                                <i class="bi bi-person-plus-fill me-2"></i>Add New Contact
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-warning w-100 mb-2" onclick="showCommunication()">
                                                <i class="bi bi-chat-dots-fill me-2"></i>Send Message
                                            </button>
                                        </div>

                                        <div class="col-md-3">
                                            <button class="btn btn-info w-100 mb-2" onclick="showReports()">
                                                <i class="bi bi-graph-up me-2"></i>View Reports
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card bg-dark border-secondary">
                                <div class="card-header">
                                    <h5 class="mb-0">System Status</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>Database Connection:</strong> <span class="text-success" id="dbStatus">Connected</span></p>
                                            <p><strong>Last Data Update:</strong> <span id="lastUpdate">Loading...</span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>API Server:</strong> <span class="text-success" id="apiStatus">Online</span></p>
                                            <p><strong>Total Records:</strong> <span id="totalRecords">Loading...</span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Message Section (Hidden by default) -->
                <div id="quickMessageSection" class="d-none" style="padding-top: 10px;">
                    <!-- Message Composer Header -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-dark border-secondary">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-chat-dots-fill me-2" style="color: #fd7e14;"></i>WhatsApp Quick Message</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Recipient Selection -->
                                    <div class="row mb-4">
                                        <div class="col-md-12">
                                            <label class="form-label"><i class="bi bi-people-fill me-2"></i>Select Recipients</label>
                                            <div class="row">
                                                <!-- Filter Options -->
                                                <div class="col-md-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="recipientFilter" id="filterWhatsApp" value="whatsapp" checked>
                                                        <label class="form-check-label" for="filterWhatsApp">
                                                            <i class="bi bi-whatsapp me-1" style="color: #25D366;"></i>WhatsApp Verified Only
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="recipientFilter" id="filterGroup" value="group">
                                                        <label class="form-check-label" for="filterGroup">
                                                            <i class="bi bi-collection-fill me-1" style="color: #ffc107;"></i>Specific Group
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="recipientFilter" id="filterBirthday" value="birthday">
                                                        <label class="form-check-label" for="filterBirthday">
                                                            <i class="bi bi-calendar-event-fill me-1" style="color: #e91e63;"></i>Birthday Boys Today
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="recipientFilter" id="filterCustom" value="custom">
                                                        <label class="form-check-label" for="filterCustom">
                                                            <i class="bi bi-person-check-fill me-1" style="color: #17a2b8;"></i>Custom Selection
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Group Selection (Hidden by default) -->
                                    <div class="row mb-3 d-none" id="groupSelectionRow">
                                        <div class="col-md-6">
                                            <label for="groupSelect" class="form-label">Select Group</label>
                                            <select class="form-select bg-dark text-light" id="groupSelect">
                                                <option value="">Choose a group...</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Custom Selection (Hidden by default) -->
                                    <div class="row mb-3 d-none" id="customSelectionRow">
                                        <div class="col-md-12">
                                            <label class="form-label">Select Individual Brothers</label>
                                            <div class="border rounded p-3 bg-secondary" style="max-height: 200px; overflow-y: auto;" id="customBrothersList">
                                                <!-- Will be populated dynamically -->
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Selected Recipients Display -->
                                    <div class="row mb-3">
                                        <div class="col-md-12">
                                            <label class="form-label">Selected Recipients (<span id="recipientCount">0</span>)</label>
                                            <div class="border rounded p-2 bg-secondary" style="min-height: 60px;" id="selectedRecipients">
                                                <small class="text-muted">No recipients selected</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Message Composer -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-dark border-secondary">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-pencil-square me-2" style="color: #28a745;"></i>Compose Message</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Message Type Tabs -->
                                    <ul class="nav nav-tabs mb-3" id="messageTypeTabs" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="text-tab" data-bs-toggle="tab" data-bs-target="#text-pane" type="button" role="tab">
                                                <i class="bi bi-chat-text me-1"></i>Text
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="image-tab" data-bs-toggle="tab" data-bs-target="#image-pane" type="button" role="tab">
                                                <i class="bi bi-image me-1"></i>Image + Caption
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="location-tab" data-bs-toggle="tab" data-bs-target="#location-pane" type="button" role="tab">
                                                <i class="bi bi-geo-alt me-1"></i>Location
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="video-tab" data-bs-toggle="tab" data-bs-target="#video-pane" type="button" role="tab">
                                                <i class="bi bi-camera-video me-1"></i>Video
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="birthday-tab" data-bs-toggle="tab" data-bs-target="#birthday-pane" type="button" role="tab">
                                                <i class="bi bi-gift me-1"></i>Birthday
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="datacleanse-tab" data-bs-toggle="tab" data-bs-target="#datacleanse-pane" type="button" role="tab">
                                                <i class="bi bi-clipboard-data me-1"></i>Data Cleanse
                                            </button>
                                        </li>
                                    </ul>

                                    <!-- Tab Content -->
                                    <div class="tab-content" id="messageTypeContent">
                                        <!-- Text Message -->
                                        <div class="tab-pane fade show active" id="text-pane" role="tabpanel">
                                            <div class="mb-3">
                                                <label for="textMessage" class="form-label">Message Text</label>
                                                <textarea class="form-control bg-secondary text-light" id="textMessage" rows="4" placeholder="Type your message here..."></textarea>
                                                <div class="form-text">
                                                    <small><span id="textCharCount">0</span>/1000 characters</small>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="insertEmoji('👋')">👋</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="insertEmoji('😊')">😊</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="insertEmoji('🙏')">🙏</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="insertEmoji('💪')">💪</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="insertEmoji('🎉')">🎉</button>
                                            </div>
                                        </div>

                                        <!-- Image + Caption -->
                                        <div class="tab-pane fade" id="image-pane" role="tabpanel">
                                            <div class="mb-3">
                                                <label for="imageFile" class="form-label">Select Image</label>
                                                <input class="form-control bg-secondary text-light" type="file" id="imageFile" accept="image/*">
                                            </div>
                                            <div class="mb-3">
                                                <label for="imageCaption" class="form-label">Caption (Optional)</label>
                                                <textarea class="form-control bg-secondary text-light" id="imageCaption" rows="3" placeholder="Add a caption to your image..."></textarea>
                                            </div>
                                            <div id="imagePreview" class="mb-3 d-none">
                                                <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                                            </div>
                                        </div>

                                        <!-- Location -->
                                        <div class="tab-pane fade" id="location-pane" role="tabpanel">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="locationName" class="form-label">Location Name</label>
                                                        <input type="text" class="form-control bg-secondary text-light" id="locationName" placeholder="e.g., Brotherhood Hall">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="locationAddress" class="form-label">Address</label>
                                                        <input type="text" class="form-control bg-secondary text-light" id="locationAddress" placeholder="Full address">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="locationLat" class="form-label">Latitude</label>
                                                        <input type="number" step="any" class="form-control bg-secondary text-light" id="locationLat" placeholder="-26.2041">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="locationLng" class="form-label">Longitude</label>
                                                        <input type="number" step="any" class="form-control bg-secondary text-light" id="locationLng" placeholder="28.0473">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <button type="button" class="btn btn-outline-info btn-sm" onclick="getCurrentLocation()">
                                                    <i class="bi bi-geo-alt-fill me-1"></i>Use Current Location
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Video -->
                                        <div class="tab-pane fade" id="video-pane" role="tabpanel">
                                            <div class="mb-3">
                                                <label for="videoFile" class="form-label">Select Video</label>
                                                <input class="form-control bg-secondary text-light" type="file" id="videoFile" accept="video/*">
                                                <div class="form-text">
                                                    <small>Maximum file size: 16MB</small>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="videoCaption" class="form-label">Caption (Optional)</label>
                                                <textarea class="form-control bg-secondary text-light" id="videoCaption" rows="2" placeholder="Add a caption to your video..."></textarea>
                                            </div>
                                            <div id="videoPreview" class="mb-3 d-none">
                                                <video id="previewVideo" controls style="max-width: 300px; max-height: 200px;"></video>
                                            </div>
                                        </div>

                                        <!-- Birthday Message -->
                                        <div class="tab-pane fade" id="birthday-pane" role="tabpanel">
                                            <div class="mb-3">
                                                <label for="birthdayTemplate" class="form-label">Birthday Template</label>
                                                <select class="form-select bg-secondary text-light" id="birthdayTemplate">
                                                    <option value="">Choose a template...</option>
                                                    <option value="formal">Formal Birthday Wishes</option>
                                                    <option value="friendly">Friendly Birthday Message</option>
                                                    <option value="brotherhood">Brotherhood Special</option>
                                                    <option value="custom">Custom Message</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="birthdayMessage" class="form-label">Birthday Message</label>
                                                <textarea class="form-control bg-secondary text-light" id="birthdayMessage" rows="4" placeholder="Personalized birthday message will appear here..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <small class="text-muted">
                                                    <i class="bi bi-info-circle me-1"></i>
                                                    Use {name} to personalize with the brother's first name
                                                </small>
                                            </div>
                                        </div>

                                        <!-- Data Cleanse -->
                                        <div class="tab-pane fade" id="datacleanse-pane" role="tabpanel">
                                            <div class="alert alert-info">
                                                <h6><i class="bi bi-clipboard-data me-2"></i>Brotherhood 555 - Data Cleanse System</h6>
                                                <p class="mb-2">Send personalized data verification forms to all WhatsApp verified Brothers in Afrikaans.</p>
                                                <small>Each Brother will receive their current data and can respond with "KORREK" or update via web form.</small>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">Form Configuration</label>
                                                        <div class="card bg-secondary">
                                                            <div class="card-body">
                                                                <div class="mb-2">
                                                                    <strong>🔴⚪ Brotherhood of 555 ⚪🔴</strong><br>
                                                                    <small>Your Story - HIS Glory</small>
                                                                </div>
                                                                <div class="mb-2">
                                                                    <small class="text-muted">
                                                                        📋 <strong>Includes Current Data:</strong><br>
                                                                        • Name & Contact Details<br>
                                                                        • Phone & Email<br>
                                                                        • Birthday & Group<br>
                                                                        • WhatsApp Status
                                                                    </small>
                                                                </div>
                                                                <div class="mb-2">
                                                                    <small class="text-muted">
                                                                        🔄 <strong>Response Options:</strong><br>
                                                                        • "KORREK" - All data correct<br>
                                                                        • Web Form - Update needed
                                                                    </small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">Safety & Testing</label>
                                                        <div class="card bg-secondary">
                                                            <div class="card-body">
                                                                <div class="mb-2">
                                                                    <span class="badge bg-warning text-dark">🛡️ TEST MODE ACTIVE</span>
                                                                </div>
                                                                <div class="mb-2">
                                                                    <small class="text-muted">
                                                                        <strong>Safety Features:</strong><br>
                                                                        • Only sends to 27820511229<br>
                                                                        • WhatsApp verified only<br>
                                                                        • Confirmation required<br>
                                                                        • Progress tracking
                                                                    </small>
                                                                </div>
                                                                <div class="mb-2">
                                                                    <small class="text-success">
                                                                        ✅ <strong>Ready for Testing</strong><br>
                                                                        No Brothers will be contacted
                                                                    </small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="d-grid gap-2">
                                                        <button type="button" class="btn btn-outline-info" onclick="testDataCleanseForm()">
                                                            <i class="bi bi-eye me-2"></i>Preview Data Cleanse Form
                                                        </button>
                                                        <button type="button" class="btn btn-warning" onclick="sendDataCleanseForms()">
                                                            <i class="bi bi-send me-2"></i>Send Data Cleanse Forms (Test Mode)
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="mt-3">
                                                <small class="text-muted">
                                                    <i class="bi bi-info-circle me-1"></i>
                                                    Forms will be sent in Afrikaans with pre-filled data from your database.
                                                    Brothers can respond via WhatsApp or update details via web form.
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Message Preview & Send -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card bg-dark border-secondary">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-eye me-2" style="color: #17a2b8;"></i>Message Preview</h6>
                                </div>
                                <div class="card-body">
                                    <div class="border rounded p-3 bg-secondary" style="min-height: 100px;" id="messagePreview">
                                        <small class="text-muted">Message preview will appear here...</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-dark border-secondary">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-send me-2" style="color: #28a745;"></i>Send Message</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-success" id="sendMessageBtn" disabled>
                                            <i class="bi bi-whatsapp me-2"></i>Send WhatsApp Message
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="saveDraftBtn">
                                            <i class="bi bi-save me-2"></i>Save as Draft
                                        </button>
                                        <button type="button" class="btn btn-outline-warning btn-sm" id="testMessageBtn">
                                            <i class="bi bi-bug me-2"></i>Test Message
                                        </button>
                                    </div>
                                    <div class="mt-3">
                                        <small class="text-muted">
                                            <i class="bi bi-info-circle me-1"></i>
                                            Messages will be sent via WhatsApp Business API
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sending Progress (Hidden by default) -->
                    <div class="row mb-4 d-none" id="sendingProgress">
                        <div class="col-12">
                            <div class="card bg-dark border-secondary">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-hourglass-split me-2" style="color: #ffc107;"></i>Sending Messages</h6>
                                </div>
                                <div class="card-body">
                                    <div class="progress mb-3">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" id="sendProgressBar"></div>
                                    </div>
                                    <div id="sendStatus">
                                        <small class="text-muted">Preparing to send messages...</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Brothers List Section (Hidden by default) -->
                <div id="brothersListSection" class="d-none" style="padding-top: 10px;">
                    <!-- Loading Spinner -->
                    <div class="loading-spinner" id="loadingSpinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <label for="groupFilter" class="form-label">Contact Group</label>
                            <select class="form-select bg-dark text-light" id="groupFilter">
                                <option value="">All</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="whatsappFilter" class="form-label">WhatsApp</label>
                            <select class="form-select bg-dark text-light" id="whatsappFilter">
                                <option value="">All</option>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="birthdayFilter" class="form-label">Birthday</label>
                            <input type="date" class="form-control bg-dark text-light" id="birthdayFilter">
                        </div>
                    </div>

                    <!-- Search -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control bg-dark text-light" placeholder="Search by Contact Number or First Name" id="searchInput">
                                <button class="btn btn-primary" type="button" id="searchButton">
                                    <i class="bi bi-search"></i> Search
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-dark" id="contactsTable">
                            <thead>
                                <tr>
                                    <th style="width: 40px;">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th style="width: 60px;">Serial</th>
                                    <th>Contact Name</th>
                                    <th>First Name</th>
                                    <th>Contact Number</th>
                                    <th>Contact Group</th>
                                    <th style="width: 80px;">WhatsApp</th>
                                    <th style="width: 100px;">Birthday</th>
                                    <th style="width: 80px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="contactsTableBody">
                                <!-- Data will be populated here -->
                            </tbody>
                        </table>
                        <!-- Compact Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="text-muted small" id="paginationInfo">
                                Showing 1-10 of 100 contacts
                            </div>
                            <div class="btn-group btn-group-sm" role="group" id="paginationControls">
                                <button type="button" class="btn btn-outline-secondary" id="prevPage" disabled>
                                    <i class="bi bi-chevron-left"></i>
                                </button>
                                <span class="btn btn-outline-secondary disabled" id="currentPageInfo">1 / 10</span>
                                <button type="button" class="btn btn-outline-secondary" id="nextPage">
                                    <i class="bi bi-chevron-right"></i>
                                </button>
                            </div>
                            <div class="d-flex align-items-center">
                                <label for="itemsPerPage" class="form-label me-2 mb-0 small">Per page:</label>
                                <select class="form-select form-select-sm bg-dark text-light" id="itemsPerPage" style="width: auto;">
                                    <option value="10">10</option>
                                    <option value="15" selected>15</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- CRUD Buttons -->
                    <div class="d-flex justify-content-end mt-3 mb-3">
                        <div class="row g-2" style="width: auto;">
                            <div class="col-4">
                                <button type="button" class="btn btn-sm w-100 crud-btn" id="addButton" data-bs-toggle="modal" data-bs-target="#crudModal">
                                    <i class="bi bi-plus-circle"></i> Add
                                </button>
                            </div>
                            <div class="col-4">
                                <button class="btn btn-sm w-100 crud-btn" id="editButton" disabled>
                                    <i class="bi bi-pencil"></i> Edit
                                </button>
                            </div>
                            <div class="col-4">
                                <button class="btn btn-sm w-100 crud-btn" id="deleteButton" disabled>
                                    <i class="bi bi-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal Structure -->
                <div class="modal fade" id="crudModal" tabindex="-1" aria-labelledby="crudModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content bg-dark text-light">
                            <div class="modal-header">
                                <h5 class="modal-title" id="crudModalLabel">Add/Edit Contact</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="crudForm">
                                    <!-- Adjust form fields as needed for contacts -->
                                    <input type="hidden" id="contactId"> <!-- For editing -->
                                    <div class="mb-3">
                                        <label for="contactName" class="form-label">Contact Name</label>
                                        <input type="text" class="form-control bg-secondary text-light" id="contactName" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="firstName" class="form-label">First Name</label>
                                        <input type="text" class="form-control bg-secondary text-light" id="firstName">
                                    </div>
                                    <div class="mb-3">
                                        <label for="contactNumber" class="form-label">Contact Number</label>
                                        <input type="tel" class="form-control bg-secondary text-light" id="contactNumber" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="contactGroup" class="form-label">Contact Group</label>
                                        <select class="form-select bg-secondary text-light" id="contactGroup">
                                            <!-- Options populated dynamically -->
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="contactWhatsApp" class="form-label">WhatsApp</label>
                                        <select class="form-select bg-secondary text-light" id="contactWhatsApp">
                                            <option value="Yes">Yes</option>
                                            <option value="No">No</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="contactBirthday" class="form-label">Birthday</label>
                                        <input type="date" class="form-control bg-secondary text-light" id="contactBirthday">
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-primary" id="saveContactButton">Save Contact</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> <!-- End Main Content Area -->
    </div> <!-- End Main Application -->

    <!-- Footer Bar -->
    <div class="footer-bar">
        <div class="copyright">© 2025 Brotherhood of 555. All rights reserved.</div>
        <div class="message-area" id="messageArea">Welcome to the Contact Management System</div>
        <div class="version">Version 1.0.0</div>
    </div>

    <!-- Ensure Bootstrap loads correctly -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Ensure script.js is loaded after Bootstrap -->
    <script src="script.js" defer onerror="console.error('Failed to load script.js')"></script>

    <script>
        window.addEventListener("load", function() {
            if (typeof bootstrap !== "undefined" && typeof bootstrap.Modal !== "undefined") {
                console.log("Bootstrap Modal is properly loaded.");
            } else {
                console.error("Bootstrap Modal is not properly loaded. Make sure Bootstrap is available.");
            }
            // Remove automatic fetchContacts call since we now load dashboard first
            console.log("Application loaded successfully");
        });
    </script>
</body>
</html>
