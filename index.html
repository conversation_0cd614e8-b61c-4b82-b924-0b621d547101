<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Raleway:wght@400&display=swap" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <link rel="icon" href="data:image/x-icon;," type="image/x-icon">
    <script src="https://cdn.jsdelivr.net/npm/fingerprintjs2@2.1.4/dist/fingerprint2.min.js"></script>
    <style>
        body { font-family: 'Raleway', sans-serif; }
        .table {
            font-size: 11px;
            --bs-table-cell-padding-y: 0.25rem;
            --bs-table-cell-padding-x: 0.5rem;
        }
        .table td, .table th {
            vertical-align: middle;
            padding: 0.25rem 0.5rem;
        }
        .btn-xs {
            padding: 0.125rem 0.25rem;
            font-size: 0.75rem;
            line-height: 1.2;
        }
        .loading-spinner {
            display: none; /* Hidden by default */
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1050;
        }
        .row-selected {
            background-color: rgba(13, 110, 253, 0.25) !important;
        }
        .crud-btn {
            min-width: 80px;
            height: 38px; /* Fixed height for all buttons */
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .crud-btn#addButton {
            background-color: #198754 !important;
            border-color: #198754 !important;
            color: #ffffff !important;
        }
        .crud-btn#addButton:hover {
            background-color: #157347 !important;
            border-color: #146c43 !important;
        }
        .crud-btn#editButton {
            background-color: #0d6efd !important;
            border-color: #0d6efd !important;
            color: #ffffff !important;
        }
        .crud-btn#editButton:hover {
            background-color: #0b5ed7 !important;
            border-color: #0a58ca !important;
        }
        .crud-btn#deleteButton {
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
            color: #ffffff !important;
        }
        .crud-btn#deleteButton:hover {
            background-color: #bb2d3b !important;
            border-color: #b02a37 !important;
        }
        .crud-btn:disabled {
            background-color: #6c757d !important;
            border-color: #6c757d !important;
            color: #ffffff !important;
            opacity: 0.65;
        }
        .whatsapp-cell {
            font-weight: bold !important;
            text-align: center !important;
            color: #000000 !important;
        }
        .whatsapp-yes {
            background-color: #32CD32 !important;
            color: #000000 !important;
        }
        .whatsapp-no {
            background-color: #FF0000 !important;
            color: #000000 !important;
        }
        /* NUCLEAR OPTION - FORCE WhatsApp colors with MAXIMUM !important */
        .whatsapp-yes {
            background: #32CD32 !important;
            background-color: #32CD32 !important;
            color: #000000 !important;
            font-weight: bold !important;
            text-align: center !important;
        }

        .whatsapp-no {
            background: #FF0000 !important;
            background-color: #FF0000 !important;
            color: #000000 !important;
            font-weight: bold !important;
            text-align: center !important;
        }

        /* Override EVERYTHING with maximum specificity and !important */
        * td.whatsapp-yes,
        * tr td.whatsapp-yes,
        * tbody tr td.whatsapp-yes,
        * table tbody tr td.whatsapp-yes,
        * .table tbody tr td.whatsapp-yes,
        * .table-dark tbody tr td.whatsapp-yes,
        * tr:hover td.whatsapp-yes,
        * tr.row-selected td.whatsapp-yes,
        * .row-selected td.whatsapp-yes,
        * .table-dark tr:hover td.whatsapp-yes,
        * .table-dark tr.row-selected td.whatsapp-yes,
        * .table-striped tbody tr:nth-of-type(odd) td.whatsapp-yes,
        * .table-striped tbody tr:nth-of-type(even) td.whatsapp-yes {
            background: #32CD32 !important;
            background-color: #32CD32 !important;
            color: #000000 !important;
            font-weight: bold !important;
            text-align: center !important;
        }

        * td.whatsapp-no,
        * tr td.whatsapp-no,
        * tbody tr td.whatsapp-no,
        * table tbody tr td.whatsapp-no,
        * .table tbody tr td.whatsapp-no,
        * .table-dark tbody tr td.whatsapp-no,
        * tr:hover td.whatsapp-no,
        * tr.row-selected td.whatsapp-no,
        * .row-selected td.whatsapp-no,
        * .table-dark tr:hover td.whatsapp-no,
        * .table-dark tr.row-selected td.whatsapp-no,
        * .table-striped tbody tr:nth-of-type(odd) td.whatsapp-no,
        * .table-striped tbody tr:nth-of-type(even) td.whatsapp-no {
            background: #FF0000 !important;
            background-color: #FF0000 !important;
            color: #000000 !important;
            font-weight: bold !important;
            text-align: center !important;
        }
    </style>
</head>
<body class="bg-dark text-light">
    <!-- Top Navigation Bar -->
    <div class="top-nav-bar">
        <div class="d-flex align-items-center">
            <span id="devModeIndicator" class="dev-mode-indicator d-none">DEV MODE</span>
        </div>
        <div class="page-title" id="pageTitle">Dashboard</div>
        <div class="d-flex align-items-center">
            <span id="currentDate"></span>
        </div>
    </div>

    <!-- Login Screen - shown before authentication -->
    <div id="loginScreen" class="container-fluid vh-100 d-flex justify-content-center align-items-center">
        <div class="card bg-dark text-light" style="width: 400px;">
            <div class="card-header text-center">
                <img src="555 logo.png" width="175px" alt="555 Logo" class="my-3">
                <h3>Login</h3>
            </div>
            <div class="card-body">
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control bg-dark text-light" id="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control bg-dark text-light" id="password" required>
                    </div>
                    <div id="loginMessage" class="alert alert-danger d-none"></div>
                    <button type="submit" class="btn btn-primary w-100">Login</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Main Application - hidden until authenticated -->
    <div id="mainApplication" class="d-none">
        <!-- Sidebar -->
        <div class="sidebar bg-secondary">
            <div class="text-center p-3">
                <img src="555 logo.png" width="175px" alt="Logo">
            </div>
            <hr class="text-light">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link text-light" href="#dashboard"><i class="bi bi-speedometer2 me-2" style="color: #0d6efd;"></i>Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-light collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#brothersSubmenu" aria-expanded="false"><i class="bi bi-people-fill me-2" style="color: #198754;"></i>Brothers</a>
                    <ul class="collapse list-unstyled" id="brothersSubmenu">
                        <li><a class="nav-link text-light ms-4" href="#list-all" id="listAllBrothersLink"><i class="bi bi-list-ul me-2" style="color: #20c997;"></i>List All</a></li>
                        <li><a class="nav-link text-light ms-4" href="#update-list"><i class="bi bi-arrow-clockwise me-2" style="color: #20c997;"></i>Update List</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-light collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#commSubmenu" aria-expanded="false"><i class="bi bi-chat-dots-fill me-2" style="color: #fd7e14;"></i>Communication</a>
                    <ul class="collapse list-unstyled" id="commSubmenu">
                        <li><a class="nav-link text-light ms-4" href="#quick-message"><i class="bi bi-lightning-fill me-2" style="color: #ffc107;"></i>Quick Message</a></li>
                        <li><a class="nav-link text-light ms-4" href="#campaigns"><i class="bi bi-megaphone-fill me-2" style="color: #ffc107;"></i>Campaigns</a></li>
                        <li><a class="nav-link text-light ms-4" href="#social-media"><i class="bi bi-share-fill me-2" style="color: #ffc107;"></i>Social Media</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-light collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#adminSubmenu" aria-expanded="false"><i class="bi bi-person-badge-fill me-2" style="color: #6f42c1;"></i>Leaders and Admins</a>
                    <ul class="collapse list-unstyled" id="adminSubmenu">
                        <li><a class="nav-link text-light ms-4" href="#admin-list"><i class="bi bi-person-lines-fill me-2" style="color: #d63384;"></i>List</a></li>
                        <li><a class="nav-link text-light ms-4" href="#admin-update"><i class="bi bi-person-gear me-2" style="color: #d63384;"></i>Update</a></li>
                    </ul>
                </li>
                <li class="nav-item mt-auto">
                    <a class="nav-link text-light" href="#" id="logoutButton"><i class="bi bi-box-arrow-right me-2" style="color: #dc3545;"></i>Logout</a>
                </li>
            </ul>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <div class="container-fluid py-4">
                <!-- Dashboard Section -->
                <div id="dashboardSection">
                    <h1 class="mb-4">Dashboard</h1>

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="card-title" id="totalContactsCount">0</h4>
                                            <p class="card-text">Total Contacts</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-people-fill" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="card-title" id="whatsappYesCount">0</h4>
                                            <p class="card-text">WhatsApp Enabled</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-whatsapp" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="card-title" id="totalGroupsCount">0</h4>
                                            <p class="card-text">Contact Groups</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-collection-fill" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="card-title" id="birthdaysThisMonthCount">0</h4>
                                            <p class="card-text">Birthdays This Month</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-calendar-event-fill" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-dark border-secondary">
                                <div class="card-header">
                                    <h5 class="mb-0">Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <button class="btn btn-primary w-100 mb-2" onclick="showBrothersList()">
                                                <i class="bi bi-people-fill me-2"></i>View Brothers List
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-success w-100 mb-2" onclick="addNewContact()">
                                                <i class="bi bi-person-plus-fill me-2"></i>Add New Contact
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-warning w-100 mb-2" onclick="showCommunication()">
                                                <i class="bi bi-chat-dots-fill me-2"></i>Send Message
                                            </button>
                                        </div>
                                        <div class="col-md-3">
                                            <button class="btn btn-info w-100 mb-2" onclick="showReports()">
                                                <i class="bi bi-graph-up me-2"></i>View Reports
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card bg-dark border-secondary">
                                <div class="card-header">
                                    <h5 class="mb-0">System Status</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>Database Connection:</strong> <span class="text-success" id="dbStatus">Connected</span></p>
                                            <p><strong>Last Data Update:</strong> <span id="lastUpdate">Loading...</span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>API Server:</strong> <span class="text-success" id="apiStatus">Online</span></p>
                                            <p><strong>Total Records:</strong> <span id="totalRecords">Loading...</span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Brothers List Section (Hidden by default) -->
                <div id="brothersListSection" class="d-none" style="padding-top: 10px;">
                    <!-- Loading Spinner -->
                    <div class="loading-spinner" id="loadingSpinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <label for="groupFilter" class="form-label">Contact Group</label>
                            <select class="form-select bg-dark text-light" id="groupFilter">
                                <option value="">All</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="whatsappFilter" class="form-label">WhatsApp</label>
                            <select class="form-select bg-dark text-light" id="whatsappFilter">
                                <option value="">All</option>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="birthdayFilter" class="form-label">Birthday</label>
                            <input type="date" class="form-control bg-dark text-light" id="birthdayFilter">
                        </div>
                    </div>

                    <!-- Search -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control bg-dark text-light" placeholder="Search by Contact Number or First Name" id="searchInput">
                                <button class="btn btn-primary" type="button" id="searchButton">
                                    <i class="bi bi-search"></i> Search
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-dark" id="contactsTable">
                            <thead>
                                <tr>
                                    <th style="width: 40px;">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th style="width: 60px;">Serial</th>
                                    <th>Contact Name</th>
                                    <th>First Name</th>
                                    <th>Contact Number</th>
                                    <th>Contact Group</th>
                                    <th style="width: 80px;">WhatsApp</th>
                                    <th style="width: 100px;">Birthday</th>
                                    <th style="width: 80px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="contactsTableBody">
                                <!-- Data will be populated here -->
                            </tbody>
                        </table>
                        <!-- Compact Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="text-muted small" id="paginationInfo">
                                Showing 1-10 of 100 contacts
                            </div>
                            <div class="btn-group btn-group-sm" role="group" id="paginationControls">
                                <button type="button" class="btn btn-outline-secondary" id="prevPage" disabled>
                                    <i class="bi bi-chevron-left"></i>
                                </button>
                                <span class="btn btn-outline-secondary disabled" id="currentPageInfo">1 / 10</span>
                                <button type="button" class="btn btn-outline-secondary" id="nextPage">
                                    <i class="bi bi-chevron-right"></i>
                                </button>
                            </div>
                            <div class="d-flex align-items-center">
                                <label for="itemsPerPage" class="form-label me-2 mb-0 small">Per page:</label>
                                <select class="form-select form-select-sm bg-dark text-light" id="itemsPerPage" style="width: auto;">
                                    <option value="10">10</option>
                                    <option value="15" selected>15</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- CRUD Buttons -->
                    <div class="d-flex justify-content-end mt-3 mb-3">
                        <div class="row g-2" style="width: auto;">
                            <div class="col-4">
                                <button type="button" class="btn btn-sm w-100 crud-btn" id="addButton" data-bs-toggle="modal" data-bs-target="#crudModal">
                                    <i class="bi bi-plus-circle"></i> Add
                                </button>
                            </div>
                            <div class="col-4">
                                <button class="btn btn-sm w-100 crud-btn" id="editButton" disabled>
                                    <i class="bi bi-pencil"></i> Edit
                                </button>
                            </div>
                            <div class="col-4">
                                <button class="btn btn-sm w-100 crud-btn" id="deleteButton" disabled>
                                    <i class="bi bi-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal Structure -->
                <div class="modal fade" id="crudModal" tabindex="-1" aria-labelledby="crudModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content bg-dark text-light">
                            <div class="modal-header">
                                <h5 class="modal-title" id="crudModalLabel">Add/Edit Contact</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="crudForm">
                                    <!-- Adjust form fields as needed for contacts -->
                                    <input type="hidden" id="contactId"> <!-- For editing -->
                                    <div class="mb-3">
                                        <label for="contactName" class="form-label">Contact Name</label>
                                        <input type="text" class="form-control bg-secondary text-light" id="contactName" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="firstName" class="form-label">First Name</label>
                                        <input type="text" class="form-control bg-secondary text-light" id="firstName">
                                    </div>
                                    <div class="mb-3">
                                        <label for="contactNumber" class="form-label">Contact Number</label>
                                        <input type="tel" class="form-control bg-secondary text-light" id="contactNumber" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="contactGroup" class="form-label">Contact Group</label>
                                        <select class="form-select bg-secondary text-light" id="contactGroup">
                                            <!-- Options populated dynamically -->
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="contactWhatsApp" class="form-label">WhatsApp</label>
                                        <select class="form-select bg-secondary text-light" id="contactWhatsApp">
                                            <option value="Yes">Yes</option>
                                            <option value="No">No</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="contactBirthday" class="form-label">Birthday</label>
                                        <input type="date" class="form-control bg-secondary text-light" id="contactBirthday">
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-primary" id="saveContactButton">Save Contact</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> <!-- End Main Content Area -->
    </div> <!-- End Main Application -->

    <!-- Footer Bar -->
    <div class="footer-bar">
        <div class="copyright">© 2025 Brotherhood of 555. All rights reserved.</div>
        <div class="message-area" id="messageArea">Welcome to the Contact Management System</div>
        <div class="version">Version 1.0.0</div>
    </div>

    <!-- Ensure Bootstrap loads correctly -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Ensure script.js is loaded after Bootstrap -->
    <script src="script.js" defer onerror="console.error('Failed to load script.js')"></script>

    <script>
        window.addEventListener("load", function() {
            if (typeof bootstrap !== "undefined" && typeof bootstrap.Modal !== "undefined") {
                console.log("Bootstrap Modal is properly loaded.");
            } else {
                console.error("Bootstrap Modal is not properly loaded. Make sure Bootstrap is available.");
            }
            // Remove automatic fetchContacts call since we now load dashboard first
            console.log("Application loaded successfully");
        });
    </script>
</body>
</html>
